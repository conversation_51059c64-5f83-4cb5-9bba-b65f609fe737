document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const generateBtn = document.getElementById('generateBtn');
    const imageContainer = document.getElementById('imageContainer');
    const promptInput = document.getElementById('prompt');
    const styleChipsContainer = document.getElementById('style-chips');
    const batchSizeSlider = document.getElementById('batchSize');
    const batchSizeValue = document.getElementById('batchSizeValue');
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightbox-img');
    const closeLightboxBtn = document.querySelector('.lightbox .close-btn');
    const downloadLightboxBtn = document.getElementById('lightbox-download');

    const apiKeyInput = document.getElementById('apiKey');
    const apiKeyToggle = document.getElementById('apiKeyToggle');
    const saveApiKeyCheckbox = document.getElementById('saveApiKey');

    const BASE_URL = 'https://api.voidai.app/v1';
    const API_KEY_STORAGE_KEY = 'voidai_api_key';

    // --- INITIALIZATION ---
    const init = () => {
        loadApiKey();
    };

    // --- EVENT LISTENERS ---
    generateBtn.addEventListener('click', handleGeneration);
    styleChipsContainer.addEventListener('click', handleStyleChipClick);
    batchSizeSlider.addEventListener('input', () => batchSizeValue.textContent = batchSizeSlider.value);
    closeLightboxBtn.addEventListener('click', closeLightbox);
    apiKeyToggle.addEventListener('click', toggleApiKeyVisibility);
    saveApiKeyCheckbox.addEventListener('change', handleSaveApiKeyChange);

    // --- UI FUNCTIONS ---
    function toggleApiKeyVisibility() {
        const isPassword = apiKeyInput.type === 'password';
        apiKeyInput.type = isPassword ? 'text' : 'password';
        apiKeyToggle.className = `fas ${isPassword ? 'fa-eye-slash' : 'fa-eye'}`;
    }

    function handleSaveApiKeyChange() {
        if (saveApiKeyCheckbox.checked) {
            localStorage.setItem(API_KEY_STORAGE_KEY, apiKeyInput.value);
        } else {
            localStorage.removeItem(API_KEY_STORAGE_KEY);
        }
    }

    function loadApiKey() {
        const savedKey = localStorage.getItem(API_KEY_STORAGE_KEY);
        if (savedKey) {
            apiKeyInput.value = savedKey;
            saveApiKeyCheckbox.checked = true;
        }
    }

    function handleStyleChipClick(e) {
        if (e.target.classList.contains('style-chip')) {
            // Deselect all other chips
            styleChipsContainer.querySelectorAll('.style-chip').forEach(chip => {
                if (chip !== e.target) {
                    chip.classList.remove('active');
                }
            });
            e.target.classList.toggle('active');
        }
    }

    function setLoadingState(isLoading) {
        generateBtn.disabled = isLoading;
        if (isLoading) {
            generateBtn.querySelector('.btn-text').textContent = 'Creating';
            generateBtn.classList.add('loading');
        } else {
            generateBtn.querySelector('.btn-text').textContent = 'Generate';
            generateBtn.classList.remove('loading');
        }
    }

    // --- CORE API LOGIC ---
    async function handleGeneration() {
        const userPrompt = promptInput.value;
        const apiKey = apiKeyInput.value;
        if (!userPrompt || !apiKey) {
            alert('Please enter a prompt and API key.');
            return;
        }

        setLoadingState(true);
        const batchSize = parseInt(batchSizeSlider.value, 10);
        setupPlaceholders(batchSize);

        const selectedStyle = styleChipsContainer.querySelector('.style-chip.active')?.textContent || '';
        const fullPrompt = selectedStyle ? `${userPrompt}, ${selectedStyle}` : userPrompt;

        const promises = Array(batchSize).fill(null).map((_, i) => generateSingleImage(fullPrompt, i, Math.floor(Math.random() * 1000000)));

        try {
            await Promise.all(promises);
        } catch (error) {
            console.error('Error during generation:', error);
            alert(`An error occurred: ${error.message}`);
        } finally {
            setLoadingState(false);
        }
    }

    function setupPlaceholders(count) {
        imageContainer.innerHTML = '';
        for (let i = 0; i < count; i++) {
            const placeholder = document.createElement('div');
            placeholder.className = 'image-placeholder';
            placeholder.id = `placeholder-${i}`;
            placeholder.innerHTML = '<div class="shimmer-wrapper"></div><img />';
            imageContainer.appendChild(placeholder);
        }
    }

    async function generateSingleImage(prompt, index, seed) {
        try {
            const requestBody = {
                model: document.getElementById('model').value,
                prompt: prompt,
                n: 1,
                size: document.getElementById('size').value,
                seed: seed
            };

            const response = await fetch(`${BASE_URL}/images/generations`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKeyInput.value}` },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error.message || `HTTP Error ${response.status}`);
            }

            const data = await response.json();
            displayImage(data.data[0], index);

        } catch (error) {
            console.error('Error generating image:', error);
            displayErrorInPlaceholder(index, error.message);
        }
    }

    function displayImage(imageData, index) {
        const placeholder = document.getElementById(`placeholder-${index}`);
        if (!placeholder) return;

        const img = placeholder.querySelector('img');
        img.src = imageData.url;
        img.onload = () => {
            img.style.display = 'block';
            placeholder.querySelector('.shimmer-wrapper').style.display = 'none';
        };
        img.addEventListener('click', () => openLightbox(imageData.url, imageData.revised_prompt || promptInput.value));
    }

    function displayErrorInPlaceholder(index, message) {
        const placeholder = document.getElementById(`placeholder-${index}`);
        if (!placeholder) return;
        placeholder.classList.add('error');
        placeholder.innerHTML = `<i class="fas fa-exclamation-triangle"></i><p>${message}</p>`;
    }

    function openLightbox(imageUrl, revisedPrompt) {
        lightboxImg.src = imageUrl;
        downloadLightboxBtn.href = imageUrl;
        
        const infoElement = document.createElement('p');
        infoElement.className = 'lightbox-info';
        infoElement.textContent = revisedPrompt;
        lightbox.appendChild(infoElement);

        lightbox.classList.add('active');
    }

    function closeLightbox() {
        lightbox.classList.remove('active');
        lightboxImg.src = ''; // Clear image
        const infoElement = lightbox.querySelector('.lightbox-info');
        if (infoElement) {
            infoElement.remove();
        }
    }

    init();
});
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
    --bg-color: #121212;
    --panel-color: #1e1e1e;
    --primary-color: #f0b90b;
    --text-color: #e0e0e0;
    --light-text-color: #888;
    --border-color: #333;
}

* { box-sizing: border-box; margin: 0; padding: 0; }

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    overflow: hidden;
}

/* Responsive design improvements */
@media (max-width: 1200px) {
    .content-area {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .settings-panel {
        width: 100%;
        height: auto;
        max-height: 40vh;
        overflow-y: auto;
    }

    .content-area {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        padding: 1rem;
    }
}

.main-container { display: flex; height: 100vh; }

.settings-panel {
    width: 420px;
    background-color: var(--panel-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    padding: 2rem;
}

.logo { display: flex; align-items: center; gap: 0.75rem; margin-bottom: 2rem; }
.logo i { font-size: 1.8rem; color: var(--primary-color); }
.logo h1 { font-size: 1.5rem; font-weight: 600; }

.settings-content { flex-grow: 1; overflow-y: auto; }
.settings-group { margin-bottom: 1.5rem; }
.settings-group label { display: block; font-weight: 500; margin-bottom: 0.75rem; font-size: 1rem; }

textarea, input, select { 
    width: 100%; 
    padding: 0.75rem; 
    border: 1px solid var(--border-color); 
    border-radius: 8px; 
    font-size: 1rem; 
    background: var(--bg-color); 
    color: var(--text-color);
}
textarea { min-height: 150px; resize: vertical; }

.style-chips-container { display: flex; flex-wrap: wrap; gap: 0.5rem; }
.style-chip { padding: 0.5rem 1rem; border: 1px solid var(--border-color); border-radius: 20px; background: transparent; color: var(--text-color); cursor: pointer; transition: all 0.2s; }
.style-chip.active, .style-chip:hover { background: var(--primary-color); color: #121212; border-color: var(--primary-color); }

.input-row { display: flex; gap: 1rem; }
.icon-btn { background: var(--bg-color); border: 1px solid var(--border-color); color: var(--text-color); padding: 0.75rem; border-radius: 8px; cursor: pointer; }

.api-key-wrapper { position: relative; }
.api-key-wrapper input { padding-right: 2.5rem; }
.api-key-wrapper i { position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); cursor: pointer; color: var(--light-text-color); }

.save-key-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}
.save-key-wrapper label {
    font-size: 0.9rem;
    color: var(--light-text-color);
}

#generateBtn {
    width: 100%; padding: 1rem; background: var(--primary-color); color: #121212; border: none; border-radius: 8px;
    font-size: 1.2rem; font-weight: 600; cursor: pointer; transition: all 0.3s; margin-top: 1rem;
    display: flex; align-items: center; justify-content: center; gap: 0.5rem; position: relative;
}
#generateBtn:disabled { background: var(--light-text-color); cursor: not-allowed; }

.btn-loader { display: none; width: 20px; height: 20px; border: 3px solid rgba(0,0,0,0.3); border-top-color: #121212; border-radius: 50%; animation: spin 1s linear infinite; }
#generateBtn.loading .btn-text:after { content: 'ing...'; }
#generateBtn.loading .btn-loader { display: block; }

.content-area {
    flex-grow: 1;
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Larger minimum size for better image display */
    gap: 1.5rem;
    align-content: start;
    overflow-y: auto; /* Allow scrolling when many images */
    max-height: 100vh;
}

/* Dynamic grid adjustments based on image count */
.content-area:has(.image-placeholder:nth-child(n+9)) {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.content-area:has(.image-placeholder:nth-child(n+13)) {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

.image-placeholder {
    background-color: var(--panel-color);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    display: flex; /* Make it a flex container */
    justify-content: center; /* Center content horizontally */
    align-items: center; /* Center content vertically */
    aspect-ratio: 1 / 1; /* Give it a square aspect ratio */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.image-placeholder:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.image-placeholder.loaded {
    background-color: transparent;
}

.image-placeholder.loaded:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.image-placeholder .shimmer-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    animation: shimmer 2s infinite linear;
    background: linear-gradient(90deg, transparent 20%, rgba(255,255,255,0.05) 50%, transparent 80%);
    background-size: 200% 100%;
}

.image-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Changed back to cover for better visual appeal */
    display: none; /* Hidden until loaded */
    border-radius: 12px;
}

.image-placeholder.error {
    background-color: #440000;
    color: #ffaaaa;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 1rem;
}
.image-placeholder.error .shimmer-wrapper { display: none; }
.image-placeholder.error i { font-size: 2rem; margin-bottom: 0.5rem; }
.image-placeholder.error p { font-size: 0.8rem; }

@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
@keyframes shimmer { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }

.lightbox {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    justify-content: center;
    align-items: center;
}

.lightbox.active {
    display: flex;
}

.lightbox-content {
    max-width: 85%;
    max-height: 80%;
    border-radius: 8px;
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 40px;
    color: #fff;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

.download-btn {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color);
    color: #121212;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lightbox-info {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 0.9rem;
    max-width: 80%;
    text-align: center;
}
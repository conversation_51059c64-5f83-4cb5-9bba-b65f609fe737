.app {
  display: flex;
  height: 100vh;
  background-color: var(--bg-primary);
}

/* Sidebar */
.sidebar {
  width: 320px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

/* Prompt Section */
.prompt-section {
  margin-bottom: 8px;
}

.prompt-input {
  width: 100%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 12px 16px;
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s ease;
}

.prompt-input:focus {
  border-color: var(--border-light);
}

.prompt-input::placeholder {
  color: var(--text-muted);
}

/* Style Section */
.style-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
  display: block;
}

.style-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.style-chip {
  padding: 6px 12px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: 20px;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.style-chip:hover {
  background-color: var(--border);
  color: var(--text-primary);
}

.style-chip.active {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
}

/* Settings Section */
.settings-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Slider */
.slider {
  width: 100%;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background-color: var(--text-primary);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background-color: var(--accent);
}

/* Select */
.select {
  width: 100%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 10px 12px;
  color: var(--text-primary);
  font-size: 13px;
  font-family: inherit;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.select:focus {
  border-color: var(--border-light);
}

/* Input */
.input {
  width: 100%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 10px 12px;
  color: var(--text-primary);
  font-size: 13px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: var(--border-light);
}

.input::placeholder {
  color: var(--text-muted);
}

/* API Key Input */
.api-key-input {
  position: relative;
  display: flex;
  align-items: center;
}

.api-key-input .input {
  padding-right: 40px;
}

.toggle-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  outline: none;
}

.toggle-btn:hover {
  color: var(--text-secondary);
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  margin-top: 8px;
}

.checkbox-label input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: var(--accent);
}

/* Generate Button */
.generate-btn {
  width: 100%;
  background-color: var(--accent);
  border: none;
  border-radius: var(--radius);
  padding: 14px 20px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  margin-top: auto;
}

.generate-btn:hover:not(:disabled) {
  background-color: #ff3333;
  transform: translateY(-1px);
}

.generate-btn:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  background-color: var(--bg-primary);
}

/* Image Grid */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Responsive grid adjustments */
@media (min-width: 1400px) {
  .image-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 900px) {
  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* Image Items */
.image-item {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.image-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Loading Placeholder */
.image-placeholder {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.image-placeholder.loading {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 20%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 80%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 50vh;
    border-right: none;
    border-bottom: 1px solid var(--border);
  }

  .sidebar-content {
    padding: 16px;
    gap: 16px;
  }

  .main-content {
    padding: 16px;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
}

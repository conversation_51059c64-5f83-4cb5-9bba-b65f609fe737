/* CSS Custom Properties */
:root {
  /* GitHub-style Dark Theme Colors */
  --bg-primary: #0D1117;
  --bg-secondary: #161B22;
  --bg-tertiary: #21262D;
  --text-primary: #F0F6FC;
  --text-secondary: #8B949E;
  --text-muted: #6E7681;
  --accent: #34D399;
  --border: #21262D;
  --border-light: #30363D;

  /* Spacing and Layout */
  --radius: 6px;
  --radius-lg: 8px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;

  /* Animations */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.app {
  display: flex;
  height: 100vh;
  background-color: var(--bg-primary);
  font-family: var(--font-family);
}

/* Sidebar */
.sidebar {
  width: 300px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.sidebar-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-light);
}



/* Section Styles */
.generation-section {
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  background-color: var(--bg-tertiary);
  overflow: hidden;
}

.section-header {
  padding: 12px 16px;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border: none;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.section-icon {
  font-size: 16px;
}

.section-content {
  padding: 16px;
}

/* Modern Form Controls */
.setting-group {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.value-indicator {
  color: var(--accent);
  font-weight: 700;
}

/* Prompt Input */
.prompt-input-wrapper {
  position: relative;
}

.prompt-textarea {
  width: 100%;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 14px 16px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 400;
  font-family: inherit;
  outline: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  resize: vertical;
  min-height: 80px;
}

.prompt-textarea:focus {
  border-color: var(--accent);
  background-color: var(--bg-primary);
  box-shadow: 0 0 0 3px rgba(52, 211, 153, 0.1);
}

.prompt-textarea::placeholder {
  color: var(--text-muted);
}

.prompt-counter {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 10px;
  color: var(--text-muted);
  font-weight: 500;
  background-color: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Format Selection Grid */
.format-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.format-btn {
  padding: 12px 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  position: relative;
  overflow: hidden;
}

.format-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.format-btn:hover {
  background-color: var(--border);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.format-btn:hover::before {
  left: 100%;
}

.format-btn.active {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 211, 153, 0.4);
}

.format-icon {
  font-size: 16px;
}

.format-label {
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

/* Model Selection Styles */
.model-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.model-btn {
  padding: 12px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 12px;
  text-align: left;
  width: 100%;
}

.model-btn:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-light);
  transform: translateY(-1px);
}

.model-btn.active {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 211, 153, 0.4);
}

/* Toggle Controls */
.toggle-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 8px;
}

.toggle-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.toggle-checkbox {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 36px;
  height: 20px;
  background-color: var(--bg-tertiary);
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 14px;
  height: 14px;
  background-color: var(--text-secondary);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-checkbox:checked + .toggle-slider {
  background-color: var(--accent);
  border-color: var(--accent);
}

.toggle-checkbox:checked + .toggle-slider::before {
  transform: translateX(16px);
  background-color: white;
}

.toggle-text {
  font-weight: 500;
  color: var(--text-primary);
}

.toggle-description {
  font-size: 10px;
  color: var(--text-muted);
  margin-left: 44px;
  font-style: italic;
}

/* Debug Panel */
.debug-section {
  margin-top: 16px;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: var(--bg-secondary);
  overflow: hidden;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.debug-label {
  font-weight: 500;
  color: var(--text-primary);
}

.debug-time {
  color: var(--accent);
  font-family: monospace;
}

.debug-ratio {
  color: var(--text-muted);
  font-family: monospace;
}

.debug-prompts {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-prompt-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.debug-prompt-label {
  font-size: 10px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.debug-prompt {
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  line-height: 1.4;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border: 1px solid var(--border);
  max-height: 80px;
  overflow-y: auto;
}

.debug-prompt.original {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: var(--text-primary);
}

.debug-prompt.enhanced {
  background: rgba(52, 211, 153, 0.1);
  border-color: rgba(52, 211, 153, 0.3);
  color: var(--text-primary);
}

/* Aspect Ratio Controls */
.aspect-ratio-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
  margin-top: 8px;
}

.aspect-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 8px 4px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 10px;
  color: var(--text-secondary);
}

.aspect-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--accent);
  transform: translateY(-1px);
}

.aspect-btn.active {
  background: var(--accent);
  border-color: var(--accent);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 211, 153, 0.3);
}

.aspect-icon {
  font-size: 14px;
  opacity: 0.8;
}

.aspect-label {
  font-weight: 500;
  font-size: 9px;
}

/* Negative Prompt */
.negative-prompt-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.negative-prompt-input {
  width: 100%;
  padding: 8px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 11px;
  line-height: 1.4;
  resize: vertical;
  min-height: 40px;
  font-family: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.negative-prompt-input:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(52, 211, 153, 0.1);
}

.negative-prompt-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

.negative-prompt-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preset-btn {
  padding: 4px 6px;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  color: var(--text-muted);
  font-size: 9px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-btn:hover {
  background: var(--accent);
  border-color: var(--accent);
  color: white;
}

.model-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.model-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.model-name {
  font-size: 13px;
  font-weight: 600;
  line-height: 1.2;
}

.model-desc {
  font-size: 11px;
  opacity: 0.8;
  line-height: 1.2;
}

.model-btn.active .model-name,
.model-btn.active .model-desc {
  color: white;
}

/* Modern Slider */
.slider-wrapper {
  position: relative;
  margin: 12px 0;
}

.modern-slider {
  width: 100%;
  height: 6px;
  background: var(--bg-primary);
  border-radius: 3px;
  outline: none;
  border: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
}

.modern-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--accent);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(52, 211, 153, 0.3);
  transition: all 0.2s ease;
}

.modern-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 6px 16px rgba(52, 211, 153, 0.5);
}

.modern-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--accent);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 12px rgba(52, 211, 153, 0.3);
}

.slider-track-fill {
  position: absolute;
  top: 50%;
  left: 0;
  height: 6px;
  background: var(--accent);
  border-radius: 3px;
  transform: translateY(-50%);
  pointer-events: none;
  transition: width 0.2s ease;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 10px;
  color: var(--text-muted);
  font-weight: 500;
}

/* Modern Select */
.select-wrapper {
  position: relative;
}

.modern-select {
  width: 100%;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 12px 40px 12px 16px;
  color: var(--text-primary);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.modern-select:focus {
  border-color: var(--accent);
  background-color: var(--bg-primary);
  box-shadow: 0 0 0 3px rgba(52, 211, 153, 0.1);
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: var(--text-muted);
  pointer-events: none;
  transition: transform 0.2s ease;
}

.select-wrapper:hover .select-arrow {
  transform: translateY(-50%) rotate(180deg);
}







/* Enhanced Generate Button */
.generate-section {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid var(--border);
}

.generate-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--accent), #ff5555);
  border: none;
  border-radius: var(--radius-lg);
  padding: 16px 20px;
  color: white;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 8px 24px rgba(52, 211, 153, 0.3);
}

.generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(52, 211, 153, 0.5);
}

.generate-btn:hover::before {
  left: 100%;
}

.generate-btn:active {
  transform: translateY(0);
}

.generate-btn:disabled {
  background: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.generate-btn.generating {
  background: linear-gradient(135deg, var(--border), var(--bg-secondary));
  animation: pulse 2s infinite;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.btn-icon {
  font-size: 16px;
}

.btn-shortcut {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  opacity: 0.7;
  font-weight: 500;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: loading-spin 0.8s linear infinite;
  margin-right: 4px;
}

/* Generation Progress */
.generation-progress {
  margin-top: 12px;
  animation: slideInUp 0.3s ease-out;
}

.progress-text {
  font-size: 11px;
  color: var(--text-muted);
  text-align: center;
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.progress-bar {
  height: 4px;
  background-color: var(--bg-primary);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent), #ff5555);
  border-radius: 2px;
  animation: progress-fill 3s ease-in-out infinite;
}

@keyframes progress-fill {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}







/* Main Content */
.main-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Grid Controls */
.grid-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--border);
  margin-bottom: 8px;
  animation: slideInLeft 0.5s ease-out;
}

.grid-layout-controls {
  display: flex;
  gap: 8px;
}

.filter-sort-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-input {
  padding: 8px 12px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 400;
  outline: none;
  transition: all 0.2s ease;
  width: 200px;
}

.filter-input:focus {
  border-color: var(--border-light);
  background-color: var(--bg-secondary);
}

.filter-input::placeholder {
  color: var(--text-muted);
}

.sort-select {
  padding: 8px 12px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
}

.sort-select:focus {
  border-color: var(--border-light);
  background-color: var(--bg-secondary);
}

.layout-btn {
  padding: 10px 16px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-secondary);
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
}

.layout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.layout-btn:hover {
  background-color: var(--border);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.layout-btn:hover::before {
  left: 100%;
}

.layout-btn.active {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(52, 211, 153, 0.3);
}

.selection-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.select-all-btn {
  padding: 8px 14px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-secondary);
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.select-all-btn:hover {
  background-color: var(--border);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.selection-count {
  font-size: 12px;
  color: var(--accent);
  font-weight: 600;
  padding: 6px 12px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.batch-btn {
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.download-btn {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.download-btn:hover {
  background-color: #22c55e;
  border-color: #22c55e;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
}

.delete-btn {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.delete-btn:hover {
  background-color: #ef4444;
  border-color: #ef4444;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

/* Image Grid Base */
.image-grid {
  display: grid;
  gap: 16px;
  width: 100%;
  margin: 0 auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  justify-content: center;
  align-content: center;
  min-height: 400px;
}

/* Specific Grid Layouts */
.image-grid-1x1 {
  grid-template-columns: 1fr;
  max-width: 600px;
  justify-self: center;
  align-self: center;
}

.image-grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
  max-width: 800px;
  justify-self: center;
  align-self: center;
}

.image-grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
  max-width: 900px;
  gap: 12px;
}

.image-grid-4x4 {
  grid-template-columns: repeat(4, 1fr);
  max-width: 1000px;
  gap: 10px;
}

.image-grid-5x5 {
  grid-template-columns: repeat(5, 1fr);
  max-width: 1100px;
  gap: 8px;
}

.image-grid-6x6 {
  grid-template-columns: repeat(6, 1fr);
  max-width: 1200px;
  gap: 6px;
}

.image-grid-8x8 {
  grid-template-columns: repeat(8, 1fr);
  max-width: 1400px;
  gap: 4px;
}

/* Responsive grid adjustments */
@media (max-width: 1400px) {
  .image-grid-8x8 {
    grid-template-columns: repeat(6, 1fr);
    max-width: 1200px;
    gap: 6px;
  }
}

@media (max-width: 1200px) {
  .image-grid-8x8,
  .image-grid-6x6 {
    grid-template-columns: repeat(5, 1fr);
    max-width: 1000px;
    gap: 8px;
  }

  .image-grid-5x5 {
    max-width: 900px;
    gap: 8px;
  }

  .image-grid-4x4 {
    max-width: 900px;
    gap: 8px;
  }

  .image-grid-3x3 {
    max-width: 800px;
  }
}

@media (max-width: 900px) {
  .image-grid-8x8,
  .image-grid-6x6,
  .image-grid-5x5,
  .image-grid-4x4 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 700px;
    gap: 10px;
  }

  .image-grid-3x3 {
    max-width: 700px;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .image-grid-8x8,
  .image-grid-6x6,
  .image-grid-5x5,
  .image-grid-4x4,
  .image-grid-3x3 {
    grid-template-columns: repeat(2, 1fr);
    max-width: 500px;
    gap: 12px;
  }

  .image-grid-2x2 {
    max-width: 500px;
  }

  .image-grid-1x1 {
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .image-grid-8x8,
  .image-grid-6x6,
  .image-grid-5x5,
  .image-grid-4x4,
  .image-grid-3x3,
  .image-grid-2x2 {
    grid-template-columns: 1fr;
    max-width: 350px;
    gap: 16px;
  }
}

/* Image Items */
.image-item {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  position: relative;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  animation: slideInUp 0.6s ease-out;
}

.image-item:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.8);
}

.image-item.selected {
  border-color: var(--accent);
  box-shadow: 0 8px 32px rgba(52, 211, 153, 0.4);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.image-item:hover img {
  transform: scale(1.05);
}

/* Image Checkbox */
.image-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .image-checkbox,
.image-item.selected .image-checkbox {
  opacity: 1;
}

.image-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--accent);
  cursor: pointer;
}

/* Image Actions */
.image-actions {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: var(--accent);
  transform: scale(1.1);
}

.copy-btn:hover {
  background: var(--accent);
}

/* Loading Placeholder */
.image-placeholder {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-placeholder.loading {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

.image-placeholder.loading::before {
  content: '';
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--accent);
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

.image-placeholder.loading::after {
  content: 'Generating...';
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--text-muted);
  font-size: 11px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 20%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 80%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 60vh;
    border-right: none;
    border-bottom: 1px solid var(--border);
    background: var(--bg-secondary);
  }

  .sidebar.compact {
    width: 100%;
    height: auto;
  }

  .sidebar-content {
    padding: 16px;
    gap: 16px;
    max-height: 55vh;
  }

  .section-content {
    padding: 16px;
  }

  .generate-btn {
    padding: 14px 16px;
    font-size: 13px;
  }

  .btn-shortcut {
    display: none;
  }

  .main-content {
    padding: 16px;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
}

/* Lightbox */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: scaleIn 0.3s ease;
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 32px;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.lightbox-close:hover {
  background-color: var(--bg-secondary);
  transform: scale(1.1);
}

.lightbox-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
}

.lightbox-info {
  margin-top: 20px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
  max-width: 600px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
  border-top: 1px solid var(--border);
  margin-top: 24px;
  animation: slideInUp 0.5s ease-out;
}

.pagination-info {
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
}

.pagination-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-btn {
  padding: 8px 12px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  min-width: 36px;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--border);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-indicator {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: 600;
  padding: 0 16px;
}

.per-page-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
}

.per-page-select {
  padding: 6px 8px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  color: var(--text-primary);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
}

.per-page-select:focus {
  border-color: var(--border-light);
  background-color: var(--bg-secondary);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: none;
}

.toast {
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: toastSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.toast-success {
  border-color: var(--accent);
  background: linear-gradient(135deg, var(--bg-secondary), rgba(52, 211, 153, 0.1));
}

.toast-error {
  border-color: #ef4444;
  background: linear-gradient(135deg, var(--bg-secondary), rgba(239, 68, 68, 0.1));
}

.toast-warning {
  border-color: #f59e0b;
  background: linear-gradient(135deg, var(--bg-secondary), rgba(245, 158, 11, 0.1));
}

.toast-info {
  border-color: #3b82f6;
  background: linear-gradient(135deg, var(--bg-secondary), rgba(59, 130, 246, 0.1));
}

.toast-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.toast-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

@keyframes toastSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.app {
  display: flex;
  height: 100vh;
  background-color: var(--bg-primary);
}

/* Sidebar */
.sidebar {
  width: 280px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

.sidebar-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

/* Prompt Section */
.prompt-section {
  margin-bottom: 8px;
}

.prompt-input {
  width: 100%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 14px 16px;
  color: var(--text-primary);
  font-size: 15px;
  font-weight: 400;
  font-family: inherit;
  outline: none;
  transition: all 0.2s ease;
}

.prompt-input:focus {
  border-color: var(--border-light);
  background-color: var(--bg-secondary);
}

.prompt-input::placeholder {
  color: var(--text-muted);
  font-weight: 300;
}

/* Style Section */
.style-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 10px;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.style-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.style-chip {
  padding: 8px 14px;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: 24px;
  color: var(--text-secondary);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  letter-spacing: 0.3px;
}

.style-chip:hover {
  background-color: var(--border);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.style-chip.active {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 68, 68, 0.3);
}

/* Settings Section */
.settings-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Slider */
.slider {
  width: 100%;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background-color: var(--text-primary);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background-color: var(--accent);
}

/* Select */
.select {
  width: 100%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 10px 12px;
  color: var(--text-primary);
  font-size: 13px;
  font-family: inherit;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.select:focus {
  border-color: var(--border-light);
}

/* Input */
.input {
  width: 100%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 10px 12px;
  color: var(--text-primary);
  font-size: 13px;
  font-family: inherit;
  outline: none;
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: var(--border-light);
}

.input::placeholder {
  color: var(--text-muted);
}

/* API Key Input */
.api-key-input {
  position: relative;
  display: flex;
  align-items: center;
}

.api-key-input .input {
  padding-right: 40px;
}

.toggle-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  outline: none;
}

.toggle-btn:hover {
  color: var(--text-secondary);
}

/* Checkbox */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  margin-top: 8px;
}

.checkbox-label input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: var(--accent);
}

/* Generate Button */
.generate-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--accent) 0%, #ff3333 100%);
  border: none;
  border-radius: var(--radius);
  padding: 16px 20px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  margin-top: auto;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
}

.generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.generate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff3333 0%, #ff2222 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 68, 68, 0.4);
}

.generate-btn:hover:not(:disabled)::before {
  left: 100%;
}

.generate-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Image Grid */
.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px;
  width: 100%;
  max-width: 800px;
  height: 100%;
  max-height: 600px;
  margin: 0 auto;
}

/* Responsive grid adjustments */
@media (max-width: 1200px) {
  .image-grid {
    max-width: 700px;
    max-height: 500px;
  }
}

@media (max-width: 900px) {
  .image-grid {
    max-width: 600px;
    max-height: 450px;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
    max-width: 400px;
    max-height: 80vh;
  }
}

/* Image Items */
.image-item {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  position: relative;
  width: 100%;
  height: 100%;
}

.image-item:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.8);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.image-item:hover img {
  transform: scale(1.05);
}

/* Loading Placeholder */
.image-placeholder {
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  width: 100%;
  height: 100%;
}

.image-placeholder.loading {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 20%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 80%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 50vh;
    border-right: none;
    border-bottom: 1px solid var(--border);
  }

  .sidebar-content {
    padding: 16px;
    gap: 16px;
  }

  .main-content {
    padding: 16px;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
}

/* Lightbox */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: scaleIn 0.3s ease;
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 32px;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.lightbox-close:hover {
  background-color: var(--bg-secondary);
  transform: scale(1.1);
}

.lightbox-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
}

.lightbox-info {
  margin-top: 20px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
  max-width: 600px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

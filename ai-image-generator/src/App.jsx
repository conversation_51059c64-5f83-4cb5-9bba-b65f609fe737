import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [prompt, setPrompt] = useState('')
  const [batchSize, setBatchSize] = useState(4)
  const [isGenerating, setIsGenerating] = useState(false)
  const [images, setImages] = useState([])
  const [lightboxImage, setLightboxImage] = useState(null)
  const [gridLayout, setGridLayout] = useState('auto') // 'auto', '2x2', '3x3', '4x4'
  const [selectedImages, setSelectedImages] = useState(new Set())
  const [viewMode, setViewMode] = useState('grid') // 'grid', 'list'
  const [sortBy, setSortBy] = useState('newest') // 'newest', 'oldest', 'prompt'
  const [filterPrompt, setFilterPrompt] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [imagesPerPage, setImagesPerPage] = useState(16)
  const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false)
  const [selectedFormat, setSelectedFormat] = useState('1024x1024') // Default to 1:1 format
  const [imageSessions, setImageSessions] = useState([]) // Store generation sessions
  const [currentCategory, setCurrentCategory] = useState('all') // Current viewing category



  // VoidAI API configuration
  const VOIDAI_API_KEY = 'sk-voidai-H0xcb1TAflPTH7TsKUbmg3jkpsbIecxhlkUD0NBY0ENcw2RVxSCgUk58edAAU7OoyjBfi7OpxA7B0dvccssHmpAlA6EjRWTXWMhy-premium'

  const BASE_URL = 'https://api.voidai.app/v1'

  // Prompt enhancement system prompt
  const ENHANCEMENT_SYSTEM_PROMPT = `You are an expert AI prompt engineer specializing in optimizing prompts for image generation models. Your task is to transform user input into a sophisticated JSON structure that maximizes image generation quality.

Transform the user's input into this exact JSON format:
{
  "enhanced_prompt": "A detailed, optimized prompt with specific visual elements, lighting, composition, and style details",
  "negative_prompt": "Elements to avoid in the image generation",
  "style_modifiers": ["specific", "style", "keywords"],
  "technical_parameters": {
    "composition": "description of ideal composition",
    "lighting": "lighting setup description",
    "quality_tags": ["high quality", "detailed", "professional"]
  }
}

Rules:
1. Enhance the prompt with specific visual details, professional photography/art terms
2. Add appropriate negative prompts to avoid common issues
3. Include relevant style modifiers that improve image quality
4. Specify technical parameters for optimal results
5. Keep the enhanced prompt under 200 words but rich in detail
6. Focus on visual clarity, composition, and professional quality

Return ONLY the JSON object, no additional text.`

  // Keyboard support for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && lightboxImage) {
        setLightboxImage(null)
      }
    }

    if (lightboxImage) {
      document.addEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [lightboxImage])

  // Determine optimal grid layout based on image count
  const getOptimalGridLayout = (imageCount) => {
    if (gridLayout !== 'auto') return gridLayout

    if (imageCount <= 1) return '1x1'
    if (imageCount <= 4) return '2x2'
    if (imageCount <= 9) return '3x3'
    if (imageCount <= 16) return '4x4'
    if (imageCount <= 25) return '5x5'
    if (imageCount <= 36) return '6x6'
    return '8x8' // For very large sets (up to 50+ images)
  }

  // Get grid CSS class based on layout and image count
  const getGridClass = () => {
    const layout = getOptimalGridLayout(images.length)
    return `image-grid image-grid-${layout}`
  }

  // Toggle image selection
  const toggleImageSelection = (imageId) => {
    const newSelected = new Set(selectedImages)
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId)
    } else {
      newSelected.add(imageId)
    }
    setSelectedImages(newSelected)
  }

  // Select all images
  const selectAllImages = () => {
    if (selectedImages.size === images.length) {
      setSelectedImages(new Set())
    } else {
      setSelectedImages(new Set(images.map(img => img.id)))
    }
  }

  // Download single image
  const downloadImage = async (image) => {
    try {
      const response = await fetch(image.url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `ai-image-${image.id}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  // Download selected images as ZIP
  const downloadSelectedImages = async () => {
    const selectedImageObjects = images.filter(img => selectedImages.has(img.id))

    if (selectedImageObjects.length === 0) {
      alert('No images selected for download')
      return
    }

    try {
      // Import JSZip dynamically
      const JSZip = (await import('jszip')).default
      const zip = new JSZip()

      // Add each image to the ZIP
      for (let i = 0; i < selectedImageObjects.length; i++) {
        const image = selectedImageObjects[i]
        try {
          const response = await fetch(image.url)
          const blob = await response.blob()
          const filename = `ai_image_${i + 1}_${image.id}.png`
          zip.file(filename, blob)
        } catch (error) {
          console.warn(`Failed to download image ${i + 1}:`, error)
        }
      }

      // Generate and download the ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' })
      const url = URL.createObjectURL(zipBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ai_images_${Date.now()}.zip`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to create ZIP file:', error)
      alert('Failed to download images. Please try again.')
    }
  }

  // Delete selected images
  const deleteSelectedImages = () => {
    const newImages = images.filter(img => !selectedImages.has(img.id))
    setImages(newImages)
    setSelectedImages(new Set())
  }

  // Filter and sort images
  const getFilteredAndSortedImages = () => {
    let filteredImages = images

    // Apply prompt filter
    if (filterPrompt.trim()) {
      filteredImages = filteredImages.filter(img =>
        img.prompt.toLowerCase().includes(filterPrompt.toLowerCase())
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'oldest':
        filteredImages = [...filteredImages].sort((a, b) => a.timestamp - b.timestamp)
        break
      case 'prompt':
        filteredImages = [...filteredImages].sort((a, b) => a.prompt.localeCompare(b.prompt))
        break
      case 'newest':
      default:
        filteredImages = [...filteredImages].sort((a, b) => b.timestamp - a.timestamp)
        break
    }

    return filteredImages
  }

  // Get paginated images
  const getPaginatedImages = () => {
    const filteredImages = getFilteredAndSortedImages()
    const startIndex = (currentPage - 1) * imagesPerPage
    const endIndex = startIndex + imagesPerPage
    return filteredImages.slice(startIndex, endIndex)
  }

  // Get total pages
  const getTotalPages = () => {
    const filteredImages = getFilteredAndSortedImages()
    return Math.ceil(filteredImages.length / imagesPerPage)
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [filterPrompt, sortBy])



  // Enhance prompt using Gemini
  const enhancePrompt = async (userPrompt) => {
    if (!userPrompt.trim()) return userPrompt

    setIsEnhancingPrompt(true)
    try {
      const response = await fetch(`${BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${VOIDAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gemini-2.5-flash',
          messages: [
            {
              role: 'system',
              content: ENHANCEMENT_SYSTEM_PROMPT
            },
            {
              role: 'user',
              content: userPrompt
            }
          ],
          temperature: 0.7,
          max_tokens: 500
        })
      })

      if (!response.ok) {
        throw new Error(`Enhancement failed: ${response.status}`)
      }

      const data = await response.json()
      const enhancedContent = data.choices[0].message.content

      try {
        const enhancedData = JSON.parse(enhancedContent)
        return enhancedData.enhanced_prompt || userPrompt
      } catch (parseError) {
        console.warn('Failed to parse enhanced prompt JSON, using original:', parseError)
        return userPrompt
      }
    } catch (error) {
      console.error('Prompt enhancement failed:', error)
      return userPrompt
    } finally {
      setIsEnhancingPrompt(false)
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault()
        if (!isGenerating && !isEnhancingPrompt && prompt.trim()) {
          generateImages()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isGenerating, isEnhancingPrompt, prompt])

  const generateImages = async () => {
    if (!prompt.trim()) {
      alert('Please enter a prompt.')
      return
    }

    setIsGenerating(true)
    setImages([])

    try {
      // Enhance the prompt first
      const enhancedPrompt = await enhancePrompt(prompt)

      const promises = Array(batchSize).fill(null).map(async (_, index) => {
        const response = await fetch(`${BASE_URL}/images/generations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${VOIDAI_API_KEY}`
          },
          body: JSON.stringify({
            model: 'imagen-4.0-generate-preview-06-06',
            prompt: enhancedPrompt,
            n: 1,
            size: selectedFormat,
            seed: Math.floor(Math.random() * 1000000)
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error?.message || `HTTP Error ${response.status}`)
        }

        const data = await response.json()
        return {
          id: Date.now() + index,
          url: data.data[0].url,
          prompt: enhancedPrompt,
          originalPrompt: prompt,
          timestamp: Date.now() + index
        }
      })

      const results = await Promise.all(promises)

      // Create a new session for this generation
      const sessionId = Date.now()
      const newSession = {
        id: sessionId,
        prompt: prompt,
        enhancedPrompt: enhancedPrompt,
        format: selectedFormat,
        imageCount: batchSize,
        timestamp: sessionId,
        images: results
      }

      // Add session to sessions list
      setImageSessions(prev => [newSession, ...prev])

      // Update current images
      setImages(results)
    } catch (error) {
      console.error('Error generating images:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="app">
      <aside className="sidebar">
        <div className="sidebar-content">
          {/* Generation Settings */}
          <div className="generation-section">
            <div className="section-header">
              <h3 className="section-title">
                <span className="section-icon">⚡</span>
                Generation Settings
              </h3>
            </div>

            <div className="section-content">
              <div className="prompt-section">
                <label className="input-label">Prompt</label>
                <div className="prompt-input-wrapper">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Describe what you want to generate..."
                    className="prompt-textarea"
                    rows={3}
                  />
                  <div className="prompt-counter">
                    {prompt.length}/500
                  </div>
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">Format / Aspect Ratio</label>
                <div className="format-grid">
                  {[
                    { value: '1024x1024', label: '1:1', icon: '⬜', description: 'Square' },
                    { value: '1408x768', label: '16:9', icon: '▭', description: 'Landscape' },
                    { value: '768x1408', label: '9:16', icon: '▯', description: 'Portrait' },
                    { value: '1536x640', label: '21:9', icon: '▬', description: 'Ultrawide' }
                  ].map((format) => (
                    <button
                      key={format.value}
                      className={`format-btn ${selectedFormat === format.value ? 'active' : ''}`}
                      onClick={() => setSelectedFormat(format.value)}
                      title={`${format.description} (${format.value})`}
                    >
                      <span className="format-icon">{format.icon}</span>
                      <span className="format-label">{format.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">
                  Images: <span className="value-indicator">{batchSize}</span>
                </label>
                <div className="slider-wrapper">
                  <input
                    type="range"
                    min="1"
                    max="50"
                    value={batchSize}
                    onChange={(e) => setBatchSize(parseInt(e.target.value))}
                    className="modern-slider"
                  />
                  <div className="slider-track-fill" style={{width: `${(batchSize / 50) * 100}%`}}></div>
                  <div className="slider-labels">
                    <span>1</span>
                    <span>50</span>
                  </div>
                </div>
              </div>

              {/* Generate Button */}
              <div className="generate-section">
                <button
                  onClick={generateImages}
                  disabled={isGenerating || isEnhancingPrompt || !prompt.trim()}
                  className={`generate-btn ${isGenerating || isEnhancingPrompt ? 'generating' : ''}`}
                >
                  <span className="btn-content">
                    {isEnhancingPrompt ? (
                      <>
                        <span className="loading-spinner"></span>
                        Enhancing prompt...
                      </>
                    ) : isGenerating ? (
                      <>
                        <span className="loading-spinner"></span>
                        Generating...
                      </>
                    ) : (
                      <>
                        <span className="btn-icon">✨</span>
                        Generate Images
                      </>
                    )}
                  </span>
                  {!isGenerating && !isEnhancingPrompt && (
                    <span className="btn-shortcut">⌘ + Enter</span>
                  )}
                </button>

                {(isGenerating || isEnhancingPrompt) && (
                  <div className="generation-progress">
                    <div className="progress-text">
                      {isEnhancingPrompt
                        ? 'Optimizing your prompt with AI...'
                        : `Creating ${batchSize} image${batchSize > 1 ? 's' : ''}...`
                      }
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill"></div>
                    </div>
                  </div>
                )}
              </div>

            </div>
          </div>






        </div>
      </aside>

      <main className="main-content">
        {/* Grid Controls */}
        {images.length > 0 && (
          <div className="grid-controls">
            <div className="grid-layout-controls">
              <button
                className={`layout-btn ${gridLayout === 'auto' ? 'active' : ''}`}
                onClick={() => setGridLayout('auto')}
              >
                Auto
              </button>
              <button
                className={`layout-btn ${gridLayout === '2x2' ? 'active' : ''}`}
                onClick={() => setGridLayout('2x2')}
              >
                2×2
              </button>
              <button
                className={`layout-btn ${gridLayout === '3x3' ? 'active' : ''}`}
                onClick={() => setGridLayout('3x3')}
              >
                3×3
              </button>
              <button
                className={`layout-btn ${gridLayout === '4x4' ? 'active' : ''}`}
                onClick={() => setGridLayout('4x4')}
              >
                4×4
              </button>
            </div>

            <div className="filter-sort-controls">
              <input
                type="text"
                placeholder="Filter by prompt..."
                value={filterPrompt}
                onChange={(e) => setFilterPrompt(e.target.value)}
                className="filter-input"
              />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="prompt">By Prompt</option>
              </select>
            </div>

            <div className="selection-controls">
              <button
                className="select-all-btn"
                onClick={selectAllImages}
              >
                {selectedImages.size === images.length ? 'Deselect All' : 'Select All'}
              </button>
              {selectedImages.size > 0 && (
                <>
                  <span className="selection-count">
                    {selectedImages.size} selected
                  </span>
                  <div className="batch-actions">
                    <button
                      className="batch-btn download-btn"
                      onClick={downloadSelectedImages}
                      title="Download selected images"
                    >
                      ↓ Download
                    </button>
                    <button
                      className="batch-btn delete-btn"
                      onClick={deleteSelectedImages}
                      title="Delete selected images"
                    >
                      × Delete
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Image Grid */}
        <div className={getGridClass()}>
          {isGenerating && images.length === 0 && (
            Array(batchSize).fill(null).map((_, index) => (
              <div key={index} className="image-placeholder loading">
                <div className="loading-shimmer"></div>
              </div>
            ))
          )}
          {getPaginatedImages().map((image) => (
            <div
              key={image.id}
              className={`image-item ${selectedImages.has(image.id) ? 'selected' : ''}`}
            >
              <div className="image-checkbox">
                <input
                  type="checkbox"
                  checked={selectedImages.has(image.id)}
                  onChange={() => toggleImageSelection(image.id)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <img
                src={image.url}
                alt={image.prompt}
                onClick={() => setLightboxImage(image)}
              />
            </div>
          ))}
        </div>

        {/* Pagination Controls */}
        {images.length > imagesPerPage && (
          <div className="pagination-controls">
            <div className="pagination-info">
              <span>
                Showing {Math.min((currentPage - 1) * imagesPerPage + 1, getFilteredAndSortedImages().length)} - {Math.min(currentPage * imagesPerPage, getFilteredAndSortedImages().length)} of {getFilteredAndSortedImages().length} images
              </span>
            </div>

            <div className="pagination-buttons">
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                ««
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                ‹
              </button>

              <span className="page-indicator">
                Page {currentPage} of {getTotalPages()}
              </span>

              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}
                disabled={currentPage === getTotalPages()}
              >
                ›
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(getTotalPages())}
                disabled={currentPage === getTotalPages()}
              >
                »»
              </button>
            </div>

            <div className="per-page-controls">
              <label>Per page:</label>
              <select
                value={imagesPerPage}
                onChange={(e) => {
                  setImagesPerPage(Number(e.target.value))
                  setCurrentPage(1)
                }}
                className="per-page-select"
              >
                <option value={8}>8</option>
                <option value={16}>16</option>
                <option value={32}>32</option>
                <option value={64}>64</option>
              </select>
            </div>
          </div>
        )}
      </main>

      {/* Lightbox Modal */}
      {lightboxImage && (
        <div
          className="lightbox-overlay"
          onClick={() => setLightboxImage(null)}
        >
          <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
            <button
              className="lightbox-close"
              onClick={() => setLightboxImage(null)}
            >
              ×
            </button>
            <img
              src={lightboxImage.url}
              alt={lightboxImage.prompt}
              className="lightbox-image"
            />
            <div className="lightbox-info">
              <p>{lightboxImage.prompt}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default App

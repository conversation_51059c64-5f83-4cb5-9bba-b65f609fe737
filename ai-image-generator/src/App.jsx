import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [prompt, setPrompt] = useState('car')
  const [apiKey, setApiKey] = useState('')
  const [saveApiKey, setSaveApiKey] = useState(false)
  const [batchSize, setBatchSize] = useState(4)
  const [model, setModel] = useState('imagen-4.0-generate-preview-06-06')
  const [size, setSize] = useState('1024x1024')
  const [selectedStyle, setSelectedStyle] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [images, setImages] = useState([])
  const [showApiKey, setShowApiKey] = useState(false)
  const [lightboxImage, setLightboxImage] = useState(null)
  const [gridLayout, setGridLayout] = useState('auto') // 'auto', '2x2', '3x3', '4x4'
  const [selectedImages, setSelectedImages] = useState(new Set())
  const [viewMode, setViewMode] = useState('grid') // 'grid', 'list'
  const [sortBy, setSortBy] = useState('newest') // 'newest', 'oldest', 'prompt'
  const [filterPrompt, setFilterPrompt] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [imagesPerPage, setImagesPerPage] = useState(16)

  const BASE_URL = 'https://api.voidai.app/v1'
  const API_KEY_STORAGE_KEY = 'voidai_api_key'

  const styles = [
    'Cinematic', '3D Render', 'Minimalist', 'Anime',
    'Photorealistic', 'Fantasy', 'Abstract', 'Vintage'
  ]

  // Load API key on mount
  useEffect(() => {
    const savedKey = localStorage.getItem(API_KEY_STORAGE_KEY)
    if (savedKey) {
      setApiKey(savedKey)
      setSaveApiKey(true)
    }
  }, [])

  // Save/remove API key
  useEffect(() => {
    if (saveApiKey && apiKey) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey)
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY)
    }
  }, [saveApiKey, apiKey])

  // Keyboard support for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && lightboxImage) {
        setLightboxImage(null)
      }
    }

    if (lightboxImage) {
      document.addEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [lightboxImage])

  // Determine optimal grid layout based on image count
  const getOptimalGridLayout = (imageCount) => {
    if (gridLayout !== 'auto') return gridLayout

    if (imageCount <= 1) return '1x1'
    if (imageCount <= 4) return '2x2'
    if (imageCount <= 9) return '3x3'
    if (imageCount <= 16) return '4x4'
    return '4x4' // Default for larger sets
  }

  // Get grid CSS class based on layout and image count
  const getGridClass = () => {
    const layout = getOptimalGridLayout(images.length)
    return `image-grid image-grid-${layout}`
  }

  // Toggle image selection
  const toggleImageSelection = (imageId) => {
    const newSelected = new Set(selectedImages)
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId)
    } else {
      newSelected.add(imageId)
    }
    setSelectedImages(newSelected)
  }

  // Select all images
  const selectAllImages = () => {
    if (selectedImages.size === images.length) {
      setSelectedImages(new Set())
    } else {
      setSelectedImages(new Set(images.map(img => img.id)))
    }
  }

  // Download single image
  const downloadImage = async (image) => {
    try {
      const response = await fetch(image.url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `ai-image-${image.id}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  // Download selected images
  const downloadSelectedImages = async () => {
    const selectedImageObjects = images.filter(img => selectedImages.has(img.id))

    for (const image of selectedImageObjects) {
      await downloadImage(image)
      // Add small delay to prevent overwhelming the browser
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  // Delete selected images
  const deleteSelectedImages = () => {
    const newImages = images.filter(img => !selectedImages.has(img.id))
    setImages(newImages)
    setSelectedImages(new Set())
  }

  // Filter and sort images
  const getFilteredAndSortedImages = () => {
    let filteredImages = images

    // Apply prompt filter
    if (filterPrompt.trim()) {
      filteredImages = filteredImages.filter(img =>
        img.prompt.toLowerCase().includes(filterPrompt.toLowerCase())
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'oldest':
        filteredImages = [...filteredImages].sort((a, b) => a.timestamp - b.timestamp)
        break
      case 'prompt':
        filteredImages = [...filteredImages].sort((a, b) => a.prompt.localeCompare(b.prompt))
        break
      case 'newest':
      default:
        filteredImages = [...filteredImages].sort((a, b) => b.timestamp - a.timestamp)
        break
    }

    return filteredImages
  }

  // Get paginated images
  const getPaginatedImages = () => {
    const filteredImages = getFilteredAndSortedImages()
    const startIndex = (currentPage - 1) * imagesPerPage
    const endIndex = startIndex + imagesPerPage
    return filteredImages.slice(startIndex, endIndex)
  }

  // Get total pages
  const getTotalPages = () => {
    const filteredImages = getFilteredAndSortedImages()
    return Math.ceil(filteredImages.length / imagesPerPage)
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [filterPrompt, sortBy])

  const generateImages = async () => {
    if (!prompt.trim() || !apiKey.trim()) {
      alert('Please enter a prompt and API key.')
      return
    }

    setIsGenerating(true)
    setImages([])

    const fullPrompt = selectedStyle ? `${prompt}, ${selectedStyle}` : prompt

    try {
      const promises = Array(batchSize).fill(null).map(async (_, index) => {
        const response = await fetch(`${BASE_URL}/images/generations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model,
            prompt: fullPrompt,
            n: 1,
            size,
            seed: Math.floor(Math.random() * 1000000)
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error?.message || `HTTP Error ${response.status}`)
        }

        const data = await response.json()
        return {
          id: Date.now() + index,
          url: data.data[0].url,
          prompt: fullPrompt,
          timestamp: Date.now() + index
        }
      })

      const results = await Promise.all(promises)
      setImages(results)
    } catch (error) {
      console.error('Error generating images:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="app">
      <aside className="sidebar">
        <div className="sidebar-content">
          <div className="prompt-section">
            <input
              type="text"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your prompt..."
              className="prompt-input"
            />
          </div>

          <div className="style-section">
            <label className="section-label">Style</label>
            <div className="style-chips">
              {styles.map((style) => (
                <button
                  key={style}
                  className={`style-chip ${selectedStyle === style ? 'active' : ''}`}
                  onClick={() => setSelectedStyle(selectedStyle === style ? '' : style)}
                >
                  {style}
                </button>
              ))}
            </div>
          </div>

          <div className="settings-section">
            <div className="setting-group">
              <label className="section-label">Images: {batchSize}</label>
              <input
                type="range"
                min="1"
                max="16"
                value={batchSize}
                onChange={(e) => setBatchSize(parseInt(e.target.value))}
                className="slider"
              />
            </div>

            <div className="setting-group">
              <label className="section-label">Model</label>
              <select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                className="select"
              >
                <option value="imagen-4.0-generate-preview-06-06">Imagen 4.0</option>
                <option value="imagen-4.0-ultra-generate-preview-06-06">Imagen 4.0 Ultra</option>
              </select>
            </div>

            <div className="setting-group">
              <label className="section-label">Size</label>
              <select
                value={size}
                onChange={(e) => setSize(e.target.value)}
                className="select"
              >
                <option value="1024x1024">1:1 (1024x1024)</option>
                <option value="1408x768">16:9 (1408x768)</option>
                <option value="768x1408">9:16 (768x1408)</option>
              </select>
            </div>

            <div className="setting-group">
              <label className="section-label">API Key</label>
              <div className="api-key-input">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your API key"
                  className="input"
                />
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="toggle-btn"
                >
                  {showApiKey ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={saveApiKey}
                  onChange={(e) => setSaveApiKey(e.target.checked)}
                />
                Save API Key
              </label>
            </div>
          </div>

          <button
            onClick={generateImages}
            disabled={isGenerating}
            className="generate-btn"
          >
            {isGenerating ? 'Generating...' : 'Generate'}
          </button>
        </div>
      </aside>

      <main className="main-content">
        {/* Grid Controls */}
        {images.length > 0 && (
          <div className="grid-controls">
            <div className="grid-layout-controls">
              <button
                className={`layout-btn ${gridLayout === 'auto' ? 'active' : ''}`}
                onClick={() => setGridLayout('auto')}
              >
                Auto
              </button>
              <button
                className={`layout-btn ${gridLayout === '2x2' ? 'active' : ''}`}
                onClick={() => setGridLayout('2x2')}
              >
                2×2
              </button>
              <button
                className={`layout-btn ${gridLayout === '3x3' ? 'active' : ''}`}
                onClick={() => setGridLayout('3x3')}
              >
                3×3
              </button>
              <button
                className={`layout-btn ${gridLayout === '4x4' ? 'active' : ''}`}
                onClick={() => setGridLayout('4x4')}
              >
                4×4
              </button>
            </div>

            <div className="filter-sort-controls">
              <input
                type="text"
                placeholder="Filter by prompt..."
                value={filterPrompt}
                onChange={(e) => setFilterPrompt(e.target.value)}
                className="filter-input"
              />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="prompt">By Prompt</option>
              </select>
            </div>

            <div className="selection-controls">
              <button
                className="select-all-btn"
                onClick={selectAllImages}
              >
                {selectedImages.size === images.length ? 'Deselect All' : 'Select All'}
              </button>
              {selectedImages.size > 0 && (
                <>
                  <span className="selection-count">
                    {selectedImages.size} selected
                  </span>
                  <div className="batch-actions">
                    <button
                      className="batch-btn download-btn"
                      onClick={downloadSelectedImages}
                      title="Download selected images"
                    >
                      ↓ Download
                    </button>
                    <button
                      className="batch-btn delete-btn"
                      onClick={deleteSelectedImages}
                      title="Delete selected images"
                    >
                      × Delete
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Image Grid */}
        <div className={getGridClass()}>
          {isGenerating && images.length === 0 && (
            Array(batchSize).fill(null).map((_, index) => (
              <div key={index} className="image-placeholder loading">
                <div className="loading-shimmer"></div>
              </div>
            ))
          )}
          {getPaginatedImages().map((image) => (
            <div
              key={image.id}
              className={`image-item ${selectedImages.has(image.id) ? 'selected' : ''}`}
            >
              <div className="image-checkbox">
                <input
                  type="checkbox"
                  checked={selectedImages.has(image.id)}
                  onChange={() => toggleImageSelection(image.id)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <img
                src={image.url}
                alt={image.prompt}
                onClick={() => setLightboxImage(image)}
              />
            </div>
          ))}
        </div>

        {/* Pagination Controls */}
        {images.length > imagesPerPage && (
          <div className="pagination-controls">
            <div className="pagination-info">
              <span>
                Showing {Math.min((currentPage - 1) * imagesPerPage + 1, getFilteredAndSortedImages().length)} - {Math.min(currentPage * imagesPerPage, getFilteredAndSortedImages().length)} of {getFilteredAndSortedImages().length} images
              </span>
            </div>

            <div className="pagination-buttons">
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                ««
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                ‹
              </button>

              <span className="page-indicator">
                Page {currentPage} of {getTotalPages()}
              </span>

              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}
                disabled={currentPage === getTotalPages()}
              >
                ›
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(getTotalPages())}
                disabled={currentPage === getTotalPages()}
              >
                »»
              </button>
            </div>

            <div className="per-page-controls">
              <label>Per page:</label>
              <select
                value={imagesPerPage}
                onChange={(e) => {
                  setImagesPerPage(Number(e.target.value))
                  setCurrentPage(1)
                }}
                className="per-page-select"
              >
                <option value={8}>8</option>
                <option value={16}>16</option>
                <option value={32}>32</option>
                <option value={64}>64</option>
              </select>
            </div>
          </div>
        )}
      </main>

      {/* Lightbox Modal */}
      {lightboxImage && (
        <div
          className="lightbox-overlay"
          onClick={() => setLightboxImage(null)}
        >
          <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
            <button
              className="lightbox-close"
              onClick={() => setLightboxImage(null)}
            >
              ×
            </button>
            <img
              src={lightboxImage.url}
              alt={lightboxImage.prompt}
              className="lightbox-image"
            />
            <div className="lightbox-info">
              <p>{lightboxImage.prompt}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default App

import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [prompt, setPrompt] = useState('')
  const [batchSize, setBatchSize] = useState(4)
  const [isGenerating, setIsGenerating] = useState(false)
  const [images, setImages] = useState([])
  const [lightboxImage, setLightboxImage] = useState(null)
  const [gridLayout, setGridLayout] = useState('auto') // 'auto', '2x2', '3x3', '4x4'
  const [selectedImages, setSelectedImages] = useState(new Set())
  const [viewMode, setViewMode] = useState('grid') // 'grid', 'list'
  const [sortBy, setSortBy] = useState('newest') // 'newest', 'oldest', 'prompt'
  const [filterPrompt, setFilterPrompt] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [imagesPerPage, setImagesPerPage] = useState(16)
  const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false)
  const [selectedFormat, setSelectedFormat] = useState('1024x1024') // Default to 1:1 format
  const [selectedModel, setSelectedModel] = useState('normal') // Default to normal model
  const [imageSessions, setImageSessions] = useState([]) // Store generation sessions
  const [currentCategory, setCurrentCategory] = useState('all') // Current viewing category
  const [toasts, setToasts] = useState([]) // Toast notifications
  const [diversityMode, setDiversityMode] = useState(true) // Enable prompt diversity
  const [debugMode, setDebugMode] = useState(false) // Debug prompt enhancement
  const [lastEnhancementLog, setLastEnhancementLog] = useState(null) // Store last enhancement for display
  const [generationProgress, setGenerationProgress] = useState({ current: 0, total: 0 }) // Progress tracking
  const [aspectRatio, setAspectRatio] = useState('1:1') // Aspect ratio selection
  const [negativePrompt, setNegativePrompt] = useState('blurry, low quality, distorted, duplicate, similar') // Negative prompt



  // VoidAI API configuration
  const VOIDAI_API_KEY = 'sk-voidai-H0xcb1TAflPTH7TsKUbmg3jkpsbIecxhlkUD0NBY0ENcw2RVxSCgUk58edAAU7OoyjBfi7OpxA7B0dvccssHmpAlA6EjRWTXWMhy-premium'

  const BASE_URL = 'https://api.voidai.app/v1'

  // Prompt enhancement system prompt
  const ENHANCEMENT_SYSTEM_PROMPT = `You are an expert AI prompt engineer. Transform the user's input into an enhanced, detailed prompt for AI image generation.

Your response must be valid JSON in this exact format:
{
  "enhanced_prompt": "Enhanced detailed prompt in proper English"
}

Enhancement guidelines:
1. Write in clear, grammatically correct English
2. Add specific visual details (lighting, composition, style, quality)
3. Include professional photography/art terminology when appropriate
4. Keep the enhanced prompt detailed but under 150 words
5. Focus on visual clarity and professional quality
6. Maintain the original intent while adding descriptive elements

IMPORTANT: Return ONLY valid JSON. No additional text, explanations, or formatting.`

  // Toast notification functions
  const showToast = (message, type = 'info') => {
    const id = Date.now()
    const toast = { id, message, type }
    setToasts(prev => [...prev, toast])

    // Auto remove after 4 seconds
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id))
    }, 4000)
  }

  const removeToast = (id) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }

  // Copy prompt to clipboard
  const copyPrompt = async (prompt) => {
    try {
      await navigator.clipboard.writeText(prompt)
      showToast('Prompt copied to clipboard!', 'success')
    } catch (error) {
      showToast('Failed to copy prompt', 'error')
    }
  }

  // Generate diverse prompts for batch generation
  const generateDiversePrompts = (basePrompt, count) => {
    if (!diversityMode || count === 1) {
      return Array(count).fill(basePrompt)
    }

    const diversityVariations = [
      'with dramatic lighting',
      'in a different artistic style',
      'with unique composition',
      'from a different perspective',
      'with enhanced details',
      'in vibrant colors',
      'with soft lighting',
      'with bold contrast',
      'in a minimalist style',
      'with rich textures'
    ]

    const styleVariations = [
      'photorealistic',
      'artistic',
      'cinematic',
      'professional photography',
      'studio lighting',
      'natural lighting',
      'golden hour',
      'high contrast',
      'soft focus',
      'sharp details'
    ]

    return Array(count).fill(null).map((_, index) => {
      if (index === 0) return basePrompt // Keep original for first image

      const diversityElement = diversityVariations[index % diversityVariations.length]
      const styleElement = styleVariations[Math.floor(Math.random() * styleVariations.length)]

      return `${basePrompt}, ${diversityElement}, ${styleElement}`
    })
  }

  // Generate seeds for each image (respecting diversity mode)
  const generateSeeds = (count) => {
    const baseSeed = Date.now() + Math.floor(Math.random() * 1000)

    if (!diversityMode) {
      // When diversity is OFF, use the same seed for all images
      return Array(count).fill(baseSeed)
    }

    // When diversity is ON, generate unique seeds for each image
    return Array(count).fill(null).map((_, index) => {
      return baseSeed + (index * 1000) + Math.floor(Math.random() * 1000)
    })
  }

  // Retry mechanism for failed API calls
  const retryApiCall = async (apiCall, maxRetries = 3, delay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall()
      } catch (error) {
        if (attempt === maxRetries) {
          throw error
        }

        if (debugMode) {
          console.log(`⚠️ [DEBUG] API call failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`)
        }

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
  }

  // Batch processing with concurrency control
  const processBatch = async (items, processor, concurrency = 3) => {
    const results = []
    const executing = []

    for (const [index, item] of items.entries()) {
      const promise = processor(item, index).then(result => {
        executing.splice(executing.indexOf(promise), 1)
        return result
      })

      results.push(promise)
      executing.push(promise)

      if (executing.length >= concurrency) {
        await Promise.race(executing)
      }
    }

    return Promise.all(results)
  }

  // Aspect ratio mapping for Vertex AI Imagen
  const getImageDimensions = (aspectRatio) => {
    const ratioMap = {
      '1:1': { width: 1024, height: 1024 },
      '16:9': { width: 1408, height: 792 },
      '9:16': { width: 792, height: 1408 },
      '4:3': { width: 1152, height: 896 },
      '3:4': { width: 896, height: 1152 },
      '3:2': { width: 1216, height: 832 },
      '2:3': { width: 832, height: 1216 }
    }
    return ratioMap[aspectRatio] || ratioMap['1:1']
  }

  // Get sample image style variations (respecting diversity mode)
  const getSampleImageStyle = (index, total) => {
    if (!diversityMode) {
      // When diversity is OFF, use the same style for all images
      return 'PHOTOGRAPHIC'
    }

    // When diversity is ON, vary styles by index
    const styles = ['PHOTOGRAPHIC', 'ARTISTIC', 'CINEMATIC', 'DIGITAL_ART']
    return styles[index % styles.length]
  }

  // Keyboard support for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && lightboxImage) {
        setLightboxImage(null)
      }
    }

    if (lightboxImage) {
      document.addEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [lightboxImage])

  // Determine optimal grid layout based on image count
  const getOptimalGridLayout = (imageCount) => {
    if (gridLayout !== 'auto') return gridLayout

    if (imageCount <= 1) return '1x1'
    if (imageCount <= 4) return '2x2'
    if (imageCount <= 9) return '3x3'
    if (imageCount <= 16) return '4x4'
    if (imageCount <= 25) return '5x5'
    if (imageCount <= 36) return '6x6'
    return '8x8' // For very large sets (up to 50+ images)
  }

  // Get grid CSS class based on layout and image count
  const getGridClass = () => {
    const layout = getOptimalGridLayout(images.length)
    return `image-grid image-grid-${layout}`
  }

  // Toggle image selection
  const toggleImageSelection = (imageId) => {
    const newSelected = new Set(selectedImages)
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId)
    } else {
      newSelected.add(imageId)
    }
    setSelectedImages(newSelected)
  }

  // Select all images
  const selectAllImages = () => {
    if (selectedImages.size === images.length) {
      setSelectedImages(new Set())
    } else {
      setSelectedImages(new Set(images.map(img => img.id)))
    }
  }

  // Download single image
  const downloadImage = async (image) => {
    try {
      const response = await fetch(image.url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `ai-image-${image.id}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  // Download selected images as ZIP
  const downloadSelectedImages = async () => {
    const selectedImageObjects = images.filter(img => selectedImages.has(img.id))

    if (selectedImageObjects.length === 0) {
      alert('No images selected for download')
      return
    }

    try {
      // Import JSZip dynamically
      const JSZip = (await import('jszip')).default
      const zip = new JSZip()

      // Add each image to the ZIP
      for (let i = 0; i < selectedImageObjects.length; i++) {
        const image = selectedImageObjects[i]
        try {
          const response = await fetch(image.url)
          const blob = await response.blob()
          const filename = `ai_image_${i + 1}_${image.id}.png`
          zip.file(filename, blob)
        } catch (error) {
          console.warn(`Failed to download image ${i + 1}:`, error)
        }
      }

      // Generate and download the ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' })
      const url = URL.createObjectURL(zipBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ai_images_${Date.now()}.zip`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to create ZIP file:', error)
      alert('Failed to download images. Please try again.')
    }
  }

  // Delete selected images
  const deleteSelectedImages = () => {
    const newImages = images.filter(img => !selectedImages.has(img.id))
    setImages(newImages)
    setSelectedImages(new Set())
  }

  // Filter and sort images
  const getFilteredAndSortedImages = () => {
    let filteredImages = images

    // Apply prompt filter
    if (filterPrompt.trim()) {
      filteredImages = filteredImages.filter(img =>
        img.prompt.toLowerCase().includes(filterPrompt.toLowerCase())
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'oldest':
        filteredImages = [...filteredImages].sort((a, b) => a.timestamp - b.timestamp)
        break
      case 'prompt':
        filteredImages = [...filteredImages].sort((a, b) => a.prompt.localeCompare(b.prompt))
        break
      case 'newest':
      default:
        filteredImages = [...filteredImages].sort((a, b) => b.timestamp - a.timestamp)
        break
    }

    return filteredImages
  }

  // Get paginated images
  const getPaginatedImages = () => {
    const filteredImages = getFilteredAndSortedImages()
    const startIndex = (currentPage - 1) * imagesPerPage
    const endIndex = startIndex + imagesPerPage
    return filteredImages.slice(startIndex, endIndex)
  }

  // Get total pages
  const getTotalPages = () => {
    const filteredImages = getFilteredAndSortedImages()
    return Math.ceil(filteredImages.length / imagesPerPage)
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [filterPrompt, sortBy])



  // Enhance prompt using Gemini with debugging
  const enhancePrompt = async (userPrompt, variationIndex = 0) => {
    if (!userPrompt.trim()) return userPrompt

    setIsEnhancingPrompt(true)

    if (debugMode) {
      console.log(`🔍 [DEBUG] Original prompt ${variationIndex + 1}:`, userPrompt)
    }

    try {
      const response = await fetch(`${BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${VOIDAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gemini-2.5-flash',
          messages: [
            {
              role: 'system',
              content: ENHANCEMENT_SYSTEM_PROMPT
            },
            {
              role: 'user',
              content: userPrompt
            }
          ],
          temperature: 0.7 + (variationIndex * 0.1), // Increase variation for diversity
          max_tokens: 500
        })
      })

      if (!response.ok) {
        throw new Error(`Enhancement failed: ${response.status}`)
      }

      const data = await response.json()
      let enhancedContent = data.choices[0].message.content.trim()

      // Clean up the response to ensure valid JSON
      if (enhancedContent.startsWith('```json')) {
        enhancedContent = enhancedContent.replace(/```json\s*/, '').replace(/```\s*$/, '')
      }
      if (enhancedContent.startsWith('```')) {
        enhancedContent = enhancedContent.replace(/```\s*/, '').replace(/```\s*$/, '')
      }

      try {
        const enhancedData = JSON.parse(enhancedContent)
        const enhancedPrompt = enhancedData.enhanced_prompt

        if (enhancedPrompt && typeof enhancedPrompt === 'string' && enhancedPrompt.trim()) {
          const finalPrompt = enhancedPrompt.trim()

          if (debugMode) {
            console.log(`✨ [DEBUG] Enhanced prompt ${variationIndex + 1}:`, finalPrompt)
            console.log(`📊 [DEBUG] Enhancement ratio: ${finalPrompt.length}/${userPrompt.length} chars`)

            // Store enhancement log for UI display
            setLastEnhancementLog({
              original: userPrompt,
              enhanced: finalPrompt,
              timestamp: new Date().toLocaleTimeString(),
              variationIndex: variationIndex + 1,
              lengthRatio: `${finalPrompt.length}/${userPrompt.length}`
            })
          }

          return finalPrompt
        } else {
          console.warn('Enhanced prompt is empty or invalid, using original')
          if (debugMode) {
            console.log(`⚠️ [DEBUG] Enhancement failed for prompt ${variationIndex + 1}, using original`)
          }
          return userPrompt
        }
      } catch (parseError) {
        console.warn('Failed to parse enhanced prompt JSON:', parseError)
        console.warn('Raw content:', enhancedContent)

        // Fallback: try to extract enhanced_prompt with regex
        const match = enhancedContent.match(/"enhanced_prompt"\s*:\s*"([^"]+)"/)
        if (match && match[1]) {
          return match[1].trim()
        }

        return userPrompt
      }
    } catch (error) {
      console.error('Prompt enhancement failed:', error)
      return userPrompt
    } finally {
      setIsEnhancingPrompt(false)
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault()
        if (!isGenerating && !isEnhancingPrompt && prompt.trim()) {
          generateImages()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isGenerating, isEnhancingPrompt, prompt])

  const generateImages = async () => {
    if (!prompt.trim()) {
      showToast('Please enter a prompt to generate images.', 'warning')
      return
    }

    setIsGenerating(true)
    setImages([])

    try {
      if (debugMode) {
        console.log(`🚀 [DEBUG] Starting generation of ${batchSize} images`)
        console.log(`🎯 [DEBUG] Diversity mode: ${diversityMode ? 'ON' : 'OFF'}`)
        console.log(`🎨 [DEBUG] Model: ${selectedModel} (${selectedModel === 'ultra' ? 'imagen-4.0-generate-preview-06-06' : 'imagen-3.0-generate-001'})`)
        console.log(`📐 [DEBUG] Format: ${selectedFormat}`)
        console.log(`🔄 [DEBUG] Aspect Ratio: ${aspectRatio}`)
      }

      // Generate diverse prompts for batch
      const diversePrompts = generateDiversePrompts(prompt, batchSize)
      const seeds = generateSeeds(batchSize)

      if (debugMode) {
        console.log(`🌱 [DEBUG] Seeds generated:`, seeds)
        console.log(`🌱 [DEBUG] All seeds identical: ${seeds.every(seed => seed === seeds[0])}`)
        console.log(`📝 [DEBUG] Prompts generated:`, diversePrompts.map((p, i) => `${i + 1}: ${p.substring(0, 50)}...`))
        console.log(`📝 [DEBUG] All prompts identical: ${diversePrompts.every(p => p === diversePrompts[0])}`)
      }

      // Enhance each prompt individually for maximum diversity
      const enhancedPrompts = await Promise.all(
        diversePrompts.map((diversePrompt, index) =>
          enhancePrompt(diversePrompt, index)
        )
      )

      if (debugMode) {
        console.log(`📝 [DEBUG] Generated ${enhancedPrompts.length} enhanced prompts`)
      }

      // Initialize progress tracking
      setGenerationProgress({ current: 0, total: batchSize })

      // Create image generation function with retry logic
      const generateSingleImage = async (enhancedPrompt, index) => {
        return retryApiCall(async () => {
          const response = await fetch(`${BASE_URL}/images/generations`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${VOIDAI_API_KEY}`
            },
            body: JSON.stringify({
              model: selectedModel === 'ultra' ? 'imagen-4.0-generate-preview-06-06' : 'imagen-3.0-generate-001',
              prompt: enhancedPrompt,
              n: 1,
              size: selectedFormat,
              seed: seeds[index],
              // Advanced Vertex AI Imagen parameters
              enhancePrompt: true,
              sampleImageStyle: getSampleImageStyle(index, batchSize),
              negativePrompt: negativePrompt,
              aspectRatio: aspectRatio,
              ...getImageDimensions(aspectRatio),
              // Additional quality parameters
              guidanceScale: diversityMode ? 7.5 + (index * 0.5) : 7.5, // Vary guidance only when diversity is ON
              safetyFilterLevel: 'BLOCK_SOME',
              personGeneration: 'ALLOW_ADULT'
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error?.message || `HTTP Error ${response.status}`)
          }

          return response.json()
        }, 3, 1000)
      }

      // Process images with controlled concurrency and progress tracking
      const results = await processBatch(
        enhancedPrompts,
        async (enhancedPrompt, index) => {
          const data = await generateSingleImage(enhancedPrompt, index)

          // Update progress
          setGenerationProgress(prev => ({ ...prev, current: prev.current + 1 }))

          if (debugMode) {
            console.log(`🖼️ [DEBUG] Image ${index + 1}/${batchSize} generated successfully`)
            console.log(`🌱 [DEBUG] Seed used: ${seeds[index]}`)
            console.log(`🎨 [DEBUG] Style: ${getSampleImageStyle(index, batchSize)}`)
            console.log(`📊 [DEBUG] Guidance Scale: ${diversityMode ? 7.5 + (index * 0.5) : 7.5}`)
            console.log(`📝 [DEBUG] Prompt: ${enhancedPrompt.substring(0, 100)}...`)
          }

          return {
            id: Date.now() + index,
            url: data.data[0].url,
            prompt: enhancedPrompt,
            originalPrompt: prompt,
            timestamp: Date.now() + index,
            seed: seeds[index],
            style: getSampleImageStyle(index, batchSize)
          }
        },
        3 // Limit to 3 concurrent requests
      )

      // Reset progress
      setGenerationProgress({ current: 0, total: 0 })

      // Create a new session for this generation
      const sessionId = Date.now()
      const newSession = {
        id: sessionId,
        prompt: prompt,
        enhancedPrompt: enhancedPrompts[0] || prompt, // Use first enhanced prompt or fallback to original
        format: selectedFormat,
        model: selectedModel,
        imageCount: batchSize,
        timestamp: sessionId,
        images: results
      }

      // Add session to sessions list
      setImageSessions(prev => [newSession, ...prev])

      // Update current images
      setImages(results)
      showToast(`Successfully generated ${results.length} image${results.length > 1 ? 's' : ''}!`, 'success')
    } catch (error) {
      console.error('Error generating images:', error)
      showToast(`Generation failed: ${error.message}`, 'error')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="app">
      <aside className="sidebar">
        <div className="sidebar-content">
          {/* Generation Settings */}
          <div className="generation-section">
            <div className="section-header">
              <h3 className="section-title">
                <span className="section-icon">⚡</span>
                Generation Settings
              </h3>
            </div>

            <div className="section-content">
              <div className="prompt-section">
                <label className="input-label">Prompt</label>
                <div className="prompt-input-wrapper">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Describe what you want to generate..."
                    className="prompt-textarea"
                    rows={3}
                  />
                  <div className="prompt-counter">
                    {prompt.length}/500
                  </div>
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">Aspect Ratio</label>
                <div className="aspect-ratio-grid">
                  {[
                    { value: '1:1', label: '1:1', icon: '⬜', description: 'Square' },
                    { value: '16:9', label: '16:9', icon: '▬', description: 'Landscape' },
                    { value: '9:16', label: '9:16', icon: '▮', description: 'Portrait' },
                    { value: '4:3', label: '4:3', icon: '▭', description: 'Classic' },
                    { value: '3:4', label: '3:4', icon: '▯', description: 'Tall' },
                    { value: '3:2', label: '3:2', icon: '▬', description: 'Photo' }
                  ].map((ratio) => (
                    <button
                      key={ratio.value}
                      className={`aspect-btn ${aspectRatio === ratio.value ? 'active' : ''}`}
                      onClick={() => {
                        setAspectRatio(ratio.value)
                        // Update selectedFormat to match aspect ratio for backward compatibility
                        const formatMap = {
                          '1:1': '1024x1024',
                          '16:9': '1408x768',
                          '9:16': '768x1408',
                          '4:3': '1152x896',
                          '3:4': '896x1152',
                          '3:2': '1216x832'
                        }
                        setSelectedFormat(formatMap[ratio.value] || '1024x1024')
                      }}
                      title={`${ratio.description} (${ratio.value})`}
                    >
                      <span className="aspect-icon">{ratio.icon}</span>
                      <span className="aspect-label">{ratio.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">Model Selection</label>
                <div className="model-selection">
                  <button
                    className={`model-btn ${selectedModel === 'normal' ? 'active' : ''}`}
                    onClick={() => setSelectedModel('normal')}
                    title="Standard quality image generation"
                  >
                    <span className="model-icon">🎨</span>
                    <div className="model-info">
                      <span className="model-name">Imagen Normal</span>
                      <span className="model-desc">Standard Quality</span>
                    </div>
                  </button>
                  <button
                    className={`model-btn ${selectedModel === 'ultra' ? 'active' : ''}`}
                    onClick={() => setSelectedModel('ultra')}
                    title="Ultra high quality image generation"
                  >
                    <span className="model-icon">✨</span>
                    <div className="model-info">
                      <span className="model-name">Imagen Ultra</span>
                      <span className="model-desc">Ultra Quality</span>
                    </div>
                  </button>
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">
                  Images: <span className="value-indicator">{batchSize}</span>
                </label>
                <div className="slider-wrapper">
                  <input
                    type="range"
                    min="1"
                    max="50"
                    value={batchSize}
                    onChange={(e) => setBatchSize(parseInt(e.target.value))}
                    className="modern-slider"
                  />
                  <div className="slider-track-fill" style={{width: `${(batchSize / 50) * 100}%`}}></div>
                  <div className="slider-labels">
                    <span>1</span>
                    <span>50</span>
                  </div>
                </div>
              </div>

              {/* Negative Prompt */}
              <div className="setting-group">
                <label className="input-label">Negative Prompt</label>
                <div className="negative-prompt-section">
                  <textarea
                    value={negativePrompt}
                    onChange={(e) => setNegativePrompt(e.target.value)}
                    placeholder="What to avoid in images..."
                    className="negative-prompt-input"
                    rows="2"
                  />
                  <div className="negative-prompt-presets">
                    {[
                      'blurry, low quality',
                      'distorted, duplicate',
                      'text, watermark',
                      'nsfw, inappropriate'
                    ].map((preset) => (
                      <button
                        key={preset}
                        className="preset-btn"
                        onClick={() => setNegativePrompt(preset)}
                        title={`Use preset: ${preset}`}
                      >
                        {preset}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Diversity Controls */}
              <div className="setting-group">
                <label className="input-label">Diversity & Debug</label>
                <div className="toggle-controls">
                  <div className="toggle-item">
                    <label className="toggle-label">
                      <input
                        type="checkbox"
                        checked={diversityMode}
                        onChange={(e) => setDiversityMode(e.target.checked)}
                        className="toggle-checkbox"
                      />
                      <span className="toggle-slider"></span>
                      <span className="toggle-text">Image Diversity</span>
                    </label>
                    <span className="toggle-description">
                      {diversityMode ? 'Each image will have unique variations (different seeds, styles, prompts)' : 'All images will be identical (same seed, style, prompt)'}
                    </span>
                  </div>
                  <div className="toggle-item">
                    <label className="toggle-label">
                      <input
                        type="checkbox"
                        checked={debugMode}
                        onChange={(e) => setDebugMode(e.target.checked)}
                        className="toggle-checkbox"
                      />
                      <span className="toggle-slider"></span>
                      <span className="toggle-text">Debug Mode</span>
                    </label>
                    <span className="toggle-description">
                      {debugMode ? 'Console logging enabled' : 'Silent operation'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Generate Button */}
              <div className="generate-section">
                <button
                  onClick={generateImages}
                  disabled={isGenerating || isEnhancingPrompt || !prompt.trim()}
                  className={`generate-btn ${isGenerating || isEnhancingPrompt ? 'generating' : ''}`}
                  title="Generate images (Ctrl/Cmd + Enter)"
                >
                  <span className="btn-content">
                    {isEnhancingPrompt ? (
                      <>
                        <span className="loading-spinner"></span>
                        Enhancing prompt...
                      </>
                    ) : isGenerating ? (
                      <>
                        <span className="loading-spinner"></span>
                        Generating...
                      </>
                    ) : (
                      <>
                        <span className="btn-icon">✨</span>
                        Generate Images
                      </>
                    )}
                  </span>
                  {!isGenerating && !isEnhancingPrompt && (
                    <span className="btn-shortcut">⌘ + Enter</span>
                  )}
                </button>

                {(isGenerating || isEnhancingPrompt) && (
                  <div className="generation-progress">
                    <div className="progress-text">
                      {isEnhancingPrompt
                        ? 'Optimizing your prompt with AI...'
                        : generationProgress.total > 0
                        ? `Creating images... ${generationProgress.current}/${generationProgress.total} complete`
                        : `Creating ${batchSize} ${selectedModel === 'ultra' ? 'ultra-quality' : 'standard'} image${batchSize > 1 ? 's' : ''}...`
                      }
                    </div>
                    <div className="progress-bar">
                      <div
                        className="progress-fill"
                        style={{
                          width: generationProgress.total > 0
                            ? `${(generationProgress.current / generationProgress.total) * 100}%`
                            : '0%'
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

            </div>
          </div>
          {/* Debug Panel */}
          {debugMode && lastEnhancementLog && (
            <div className="debug-section">
              <div className="section-header">
                <h3 className="section-title">
                  <span className="section-icon">🔍</span>
                  Enhancement Debug
                </h3>
              </div>
              <div className="section-content">
                <div className="debug-info">
                  <div className="debug-item">
                    <label className="debug-label">Last Enhanced:</label>
                    <span className="debug-time">{lastEnhancementLog.timestamp}</span>
                  </div>
                  <div className="debug-item">
                    <label className="debug-label">Variation #{lastEnhancementLog.variationIndex}</label>
                    <span className="debug-ratio">({lastEnhancementLog.lengthRatio} chars)</span>
                  </div>
                  <div className="debug-prompts">
                    <div className="debug-prompt-section">
                      <label className="debug-prompt-label">Original:</label>
                      <div className="debug-prompt original">{lastEnhancementLog.original}</div>
                    </div>
                    <div className="debug-prompt-section">
                      <label className="debug-prompt-label">Enhanced:</label>
                      <div className="debug-prompt enhanced">{lastEnhancementLog.enhanced}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}






        </div>
      </aside>

      <main className="main-content">
        {/* Grid Controls */}
        {images.length > 0 && (
          <div className="grid-controls">
            <div className="grid-layout-controls">
              <button
                className={`layout-btn ${gridLayout === 'auto' ? 'active' : ''}`}
                onClick={() => setGridLayout('auto')}
              >
                Auto
              </button>
              <button
                className={`layout-btn ${gridLayout === '2x2' ? 'active' : ''}`}
                onClick={() => setGridLayout('2x2')}
              >
                2×2
              </button>
              <button
                className={`layout-btn ${gridLayout === '3x3' ? 'active' : ''}`}
                onClick={() => setGridLayout('3x3')}
              >
                3×3
              </button>
              <button
                className={`layout-btn ${gridLayout === '4x4' ? 'active' : ''}`}
                onClick={() => setGridLayout('4x4')}
              >
                4×4
              </button>
            </div>

            <div className="filter-sort-controls">
              <input
                type="text"
                placeholder="Filter by prompt..."
                value={filterPrompt}
                onChange={(e) => setFilterPrompt(e.target.value)}
                className="filter-input"
              />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="prompt">By Prompt</option>
              </select>
            </div>

            <div className="selection-controls">
              <button
                className="select-all-btn"
                onClick={selectAllImages}
              >
                {selectedImages.size === images.length ? 'Deselect All' : 'Select All'}
              </button>
              {selectedImages.size > 0 && (
                <>
                  <span className="selection-count">
                    {selectedImages.size} selected
                  </span>
                  <div className="batch-actions">
                    <button
                      className="batch-btn download-btn"
                      onClick={downloadSelectedImages}
                      title="Download selected images"
                    >
                      ↓ Download
                    </button>
                    <button
                      className="batch-btn delete-btn"
                      onClick={deleteSelectedImages}
                      title="Delete selected images"
                    >
                      × Delete
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Image Grid */}
        <div className={getGridClass()}>
          {isGenerating && images.length === 0 && (
            Array(batchSize).fill(null).map((_, index) => (
              <div key={index} className="image-placeholder loading">
                <div className="loading-shimmer"></div>
              </div>
            ))
          )}
          {getPaginatedImages().map((image) => (
            <div
              key={image.id}
              className={`image-item ${selectedImages.has(image.id) ? 'selected' : ''}`}
            >
              <div className="image-checkbox">
                <input
                  type="checkbox"
                  checked={selectedImages.has(image.id)}
                  onChange={() => toggleImageSelection(image.id)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <img
                src={image.url}
                alt={image.prompt}
                onClick={() => setLightboxImage(image)}
              />
              <div className="image-actions">
                <button
                  className="action-btn copy-btn"
                  onClick={(e) => {
                    e.stopPropagation()
                    copyPrompt(image.prompt)
                  }}
                  title="Copy prompt"
                >
                  📋
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination Controls */}
        {images.length > imagesPerPage && (
          <div className="pagination-controls">
            <div className="pagination-info">
              <span>
                Showing {Math.min((currentPage - 1) * imagesPerPage + 1, getFilteredAndSortedImages().length)} - {Math.min(currentPage * imagesPerPage, getFilteredAndSortedImages().length)} of {getFilteredAndSortedImages().length} images
              </span>
            </div>

            <div className="pagination-buttons">
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                ««
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                ‹
              </button>

              <span className="page-indicator">
                Page {currentPage} of {getTotalPages()}
              </span>

              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}
                disabled={currentPage === getTotalPages()}
              >
                ›
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(getTotalPages())}
                disabled={currentPage === getTotalPages()}
              >
                »»
              </button>
            </div>

            <div className="per-page-controls">
              <label>Per page:</label>
              <select
                value={imagesPerPage}
                onChange={(e) => {
                  setImagesPerPage(Number(e.target.value))
                  setCurrentPage(1)
                }}
                className="per-page-select"
              >
                <option value={8}>8</option>
                <option value={16}>16</option>
                <option value={32}>32</option>
                <option value={64}>64</option>
              </select>
            </div>
          </div>
        )}
      </main>

      {/* Lightbox Modal */}
      {lightboxImage && (
        <div
          className="lightbox-overlay"
          onClick={() => setLightboxImage(null)}
        >
          <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
            <button
              className="lightbox-close"
              onClick={() => setLightboxImage(null)}
            >
              ×
            </button>
            <img
              src={lightboxImage.url}
              alt={lightboxImage.prompt}
              className="lightbox-image"
            />
            <div className="lightbox-info">
              <p>{lightboxImage.prompt}</p>
            </div>
          </div>
        </div>
      )}

      {/* Toast Notifications */}
      <div className="toast-container">
        {toasts.map(toast => (
          <div
            key={toast.id}
            className={`toast toast-${toast.type}`}
            onClick={() => removeToast(toast.id)}
          >
            <span className="toast-icon">
              {toast.type === 'success' && '✅'}
              {toast.type === 'error' && '❌'}
              {toast.type === 'warning' && '⚠️'}
              {toast.type === 'info' && 'ℹ️'}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => removeToast(toast.id)}>×</button>
          </div>
        ))}
      </div>
    </div>
  )
}

export default App

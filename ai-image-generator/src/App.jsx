import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [prompt, setPrompt] = useState('car')
  const [apiKey, setApiKey] = useState('')
  const [saveApiKey, setSaveApiKey] = useState(false)
  const [batchSize, setBatchSize] = useState(4)
  const [model, setModel] = useState('imagen-4.0-generate-preview-06-06')
  const [size, setSize] = useState('1024x1024')
  const [selectedStyle, setSelectedStyle] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [images, setImages] = useState([])
  const [showApiKey, setShowApiKey] = useState(false)
  const [lightboxImage, setLightboxImage] = useState(null)
  const [gridLayout, setGridLayout] = useState('auto') // 'auto', '2x2', '3x3', '4x4'
  const [selectedImages, setSelectedImages] = useState(new Set())
  const [viewMode, setViewMode] = useState('grid') // 'grid', 'list'
  const [sortBy, setSortBy] = useState('newest') // 'newest', 'oldest', 'prompt'
  const [filterPrompt, setFilterPrompt] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [imagesPerPage, setImagesPerPage] = useState(16)

  // Sidebar state
  const [collapsedSections, setCollapsedSections] = useState({
    generation: false,
    style: false,
    advanced: true
  })
  const [sidebarCompact, setSidebarCompact] = useState(false)
  const [activePreset, setActivePreset] = useState('')

  // Presets
  const presets = [
    { name: 'Portrait', prompt: 'professional portrait, studio lighting', style: 'photorealistic', size: '768x1408', batchSize: 4 },
    { name: 'Landscape', prompt: 'beautiful landscape, golden hour', style: 'cinematic', size: '1408x768', batchSize: 4 },
    { name: 'Abstract', prompt: 'abstract art, vibrant colors', style: 'artistic', size: '1024x1024', batchSize: 9 },
    { name: 'Concept Art', prompt: 'concept art, detailed illustration', style: 'digital art', size: '1408x768', batchSize: 6 }
  ]

  const BASE_URL = 'https://api.voidai.app/v1'
  const API_KEY_STORAGE_KEY = 'voidai_api_key'

  const styles = [
    'Cinematic', '3D Render', 'Minimalist', 'Anime',
    'Photorealistic', 'Fantasy', 'Abstract', 'Vintage'
  ]

  // Load API key on mount
  useEffect(() => {
    const savedKey = localStorage.getItem(API_KEY_STORAGE_KEY)
    if (savedKey) {
      setApiKey(savedKey)
      setSaveApiKey(true)
    }
  }, [])

  // Save/remove API key
  useEffect(() => {
    if (saveApiKey && apiKey) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey)
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY)
    }
  }, [saveApiKey, apiKey])

  // Keyboard support for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && lightboxImage) {
        setLightboxImage(null)
      }
    }

    if (lightboxImage) {
      document.addEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [lightboxImage])

  // Determine optimal grid layout based on image count
  const getOptimalGridLayout = (imageCount) => {
    if (gridLayout !== 'auto') return gridLayout

    if (imageCount <= 1) return '1x1'
    if (imageCount <= 4) return '2x2'
    if (imageCount <= 9) return '3x3'
    if (imageCount <= 16) return '4x4'
    return '4x4' // Default for larger sets
  }

  // Get grid CSS class based on layout and image count
  const getGridClass = () => {
    const layout = getOptimalGridLayout(images.length)
    return `image-grid image-grid-${layout}`
  }

  // Toggle image selection
  const toggleImageSelection = (imageId) => {
    const newSelected = new Set(selectedImages)
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId)
    } else {
      newSelected.add(imageId)
    }
    setSelectedImages(newSelected)
  }

  // Select all images
  const selectAllImages = () => {
    if (selectedImages.size === images.length) {
      setSelectedImages(new Set())
    } else {
      setSelectedImages(new Set(images.map(img => img.id)))
    }
  }

  // Download single image
  const downloadImage = async (image) => {
    try {
      const response = await fetch(image.url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `ai-image-${image.id}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading image:', error)
    }
  }

  // Download selected images
  const downloadSelectedImages = async () => {
    const selectedImageObjects = images.filter(img => selectedImages.has(img.id))

    for (const image of selectedImageObjects) {
      await downloadImage(image)
      // Add small delay to prevent overwhelming the browser
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  // Delete selected images
  const deleteSelectedImages = () => {
    const newImages = images.filter(img => !selectedImages.has(img.id))
    setImages(newImages)
    setSelectedImages(new Set())
  }

  // Filter and sort images
  const getFilteredAndSortedImages = () => {
    let filteredImages = images

    // Apply prompt filter
    if (filterPrompt.trim()) {
      filteredImages = filteredImages.filter(img =>
        img.prompt.toLowerCase().includes(filterPrompt.toLowerCase())
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'oldest':
        filteredImages = [...filteredImages].sort((a, b) => a.timestamp - b.timestamp)
        break
      case 'prompt':
        filteredImages = [...filteredImages].sort((a, b) => a.prompt.localeCompare(b.prompt))
        break
      case 'newest':
      default:
        filteredImages = [...filteredImages].sort((a, b) => b.timestamp - a.timestamp)
        break
    }

    return filteredImages
  }

  // Get paginated images
  const getPaginatedImages = () => {
    const filteredImages = getFilteredAndSortedImages()
    const startIndex = (currentPage - 1) * imagesPerPage
    const endIndex = startIndex + imagesPerPage
    return filteredImages.slice(startIndex, endIndex)
  }

  // Get total pages
  const getTotalPages = () => {
    const filteredImages = getFilteredAndSortedImages()
    return Math.ceil(filteredImages.length / imagesPerPage)
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [filterPrompt, sortBy])

  // Toggle section collapse
  const toggleSection = (section) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Apply preset
  const applyPreset = (preset) => {
    setPrompt(preset.prompt)
    setSelectedStyle(preset.style)
    setSize(preset.size)
    setBatchSize(preset.batchSize)
    setActivePreset(preset.name)

    // Auto-collapse advanced section when preset is applied
    setCollapsedSections(prev => ({ ...prev, advanced: true }))
  }

  // Clear preset
  const clearPreset = () => {
    setActivePreset('')
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault()
        if (!isGenerating && prompt.trim() && apiKey.trim()) {
          generateImages()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isGenerating, prompt, apiKey])

  const generateImages = async () => {
    if (!prompt.trim() || !apiKey.trim()) {
      alert('Please enter a prompt and API key.')
      return
    }

    setIsGenerating(true)
    setImages([])

    const fullPrompt = selectedStyle ? `${prompt}, ${selectedStyle}` : prompt

    try {
      const promises = Array(batchSize).fill(null).map(async (_, index) => {
        const response = await fetch(`${BASE_URL}/images/generations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model,
            prompt: fullPrompt,
            n: 1,
            size,
            seed: Math.floor(Math.random() * 1000000)
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error?.message || `HTTP Error ${response.status}`)
        }

        const data = await response.json()
        return {
          id: Date.now() + index,
          url: data.data[0].url,
          prompt: fullPrompt,
          timestamp: Date.now() + index
        }
      })

      const results = await Promise.all(promises)
      setImages(results)
    } catch (error) {
      console.error('Error generating images:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="app">
      <aside className={`sidebar ${sidebarCompact ? 'compact' : ''}`}>
        <div className="sidebar-content">
          {/* Sidebar Header */}
          <div className="sidebar-header">
            <h2 className="sidebar-title">AI Generator</h2>
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarCompact(!sidebarCompact)}
              title={sidebarCompact ? 'Expand sidebar' : 'Compact sidebar'}
            >
              {sidebarCompact ? '→' : '←'}
            </button>
          </div>

          {/* Quick Presets */}
          {!sidebarCompact && (
            <div className="presets-section">
              <div className="section-header">
                <h3 className="section-title">Quick Presets</h3>
              </div>
              <div className="presets-grid">
                {presets.map((preset) => (
                  <button
                    key={preset.name}
                    className={`preset-btn ${activePreset === preset.name ? 'active' : ''}`}
                    onClick={() => applyPreset(preset)}
                    title={`${preset.prompt} - ${preset.style}`}
                  >
                    <span className="preset-name">{preset.name}</span>
                    <span className="preset-details">{preset.batchSize} images</span>
                  </button>
                ))}
              </div>
              {activePreset && (
                <button className="clear-preset-btn" onClick={clearPreset}>
                  Clear Preset
                </button>
              )}
            </div>
          )}

          {/* Generation Settings */}
          <div className="collapsible-section">
            <button
              className="section-header clickable"
              onClick={() => toggleSection('generation')}
            >
              <h3 className="section-title">
                <span className="section-icon">⚡</span>
                Generation Settings
              </h3>
              <span className={`collapse-icon ${collapsedSections.generation ? 'collapsed' : ''}`}>
                ▼
              </span>
            </button>

            <div className={`section-content ${collapsedSections.generation ? 'collapsed' : ''}`}>
              <div className="prompt-section">
                <label className="input-label">Prompt</label>
                <div className="prompt-input-wrapper">
                  <textarea
                    value={prompt}
                    onChange={(e) => {
                      setPrompt(e.target.value)
                      clearPreset()
                    }}
                    placeholder="Describe what you want to generate..."
                    className="prompt-textarea"
                    rows={sidebarCompact ? 2 : 3}
                  />
                  <div className="prompt-counter">
                    {prompt.length}/500
                  </div>
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">
                  Images: <span className="value-indicator">{batchSize}</span>
                </label>
                <div className="slider-wrapper">
                  <input
                    type="range"
                    min="1"
                    max="16"
                    value={batchSize}
                    onChange={(e) => {
                      setBatchSize(parseInt(e.target.value))
                      clearPreset()
                    }}
                    className="modern-slider"
                  />
                  <div className="slider-track-fill" style={{width: `${(batchSize / 16) * 100}%`}}></div>
                  <div className="slider-labels">
                    <span>1</span>
                    <span>16</span>
                  </div>
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">Model</label>
                <div className="select-wrapper">
                  <select
                    value={model}
                    onChange={(e) => {
                      setModel(e.target.value)
                      clearPreset()
                    }}
                    className="modern-select"
                  >
                    <option value="imagen-4.0-generate-preview-06-06">Imagen 4.0</option>
                    <option value="imagen-4.0-ultra-generate-preview-06-06">Imagen 4.0 Ultra</option>
                  </select>
                  <span className="select-arrow">▼</span>
                </div>
              </div>

              <div className="setting-group">
                <label className="input-label">Aspect Ratio</label>
                <div className="aspect-ratio-grid">
                  {[
                    { value: '1024x1024', label: '1:1', icon: '⬜' },
                    { value: '1408x768', label: '16:9', icon: '▭' },
                    { value: '768x1408', label: '9:16', icon: '▯' }
                  ].map((ratio) => (
                    <button
                      key={ratio.value}
                      className={`aspect-btn ${size === ratio.value ? 'active' : ''}`}
                      onClick={() => {
                        setSize(ratio.value)
                        clearPreset()
                      }}
                      title={ratio.value}
                    >
                      <span className="aspect-icon">{ratio.icon}</span>
                      <span className="aspect-label">{ratio.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Style Options */}
          <div className="collapsible-section">
            <button
              className="section-header clickable"
              onClick={() => toggleSection('style')}
            >
              <h3 className="section-title">
                <span className="section-icon">🎨</span>
                Style Options
              </h3>
              <span className={`collapse-icon ${collapsedSections.style ? 'collapsed' : ''}`}>
                ▼
              </span>
            </button>

            <div className={`section-content ${collapsedSections.style ? 'collapsed' : ''}`}>
              <div className="style-grid">
                {styles.map((style) => (
                  <button
                    key={style}
                    className={`style-chip ${selectedStyle === style ? 'active' : ''}`}
                    onClick={() => {
                      setSelectedStyle(selectedStyle === style ? '' : style)
                      clearPreset()
                    }}
                  >
                    <span className="style-name">{style}</span>
                  </button>
                ))}
              </div>
              {selectedStyle && (
                <button
                  className="clear-style-btn"
                  onClick={() => setSelectedStyle('')}
                >
                  Clear Style
                </button>
              )}
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="collapsible-section">
            <button
              className="section-header clickable"
              onClick={() => toggleSection('advanced')}
            >
              <h3 className="section-title">
                <span className="section-icon">⚙️</span>
                Advanced Settings
              </h3>
              <span className={`collapse-icon ${collapsedSections.advanced ? 'collapsed' : ''}`}>
                ▼
              </span>
            </button>

            <div className={`section-content ${collapsedSections.advanced ? 'collapsed' : ''}`}>
              <div className="setting-group">
                <label className="input-label">API Configuration</label>
                <div className="api-key-wrapper">
                  <div className="input-with-toggle">
                    <input
                      type={showApiKey ? 'text' : 'password'}
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="Enter your API key"
                      className="modern-input"
                    />
                    <button
                      type="button"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="input-toggle-btn"
                      title={showApiKey ? 'Hide API key' : 'Show API key'}
                    >
                      {showApiKey ? '👁️' : '👁️‍🗨️'}
                    </button>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={saveApiKey}
                      onChange={(e) => setSaveApiKey(e.target.checked)}
                    />
                    <span className="toggle-slider"></span>
                    <span className="toggle-label">Save API Key</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="generate-section">
            <button
              onClick={generateImages}
              disabled={isGenerating || !prompt.trim() || !apiKey.trim()}
              className={`generate-btn ${isGenerating ? 'generating' : ''}`}
            >
              <span className="btn-content">
                {isGenerating ? (
                  <>
                    <span className="loading-spinner"></span>
                    Generating...
                  </>
                ) : (
                  <>
                    <span className="btn-icon">✨</span>
                    Generate Images
                  </>
                )}
              </span>
              {!isGenerating && (
                <span className="btn-shortcut">⌘ + Enter</span>
              )}
            </button>

            {isGenerating && (
              <div className="generation-progress">
                <div className="progress-text">
                  Creating {batchSize} image{batchSize > 1 ? 's' : ''}...
                </div>
                <div className="progress-bar">
                  <div className="progress-fill"></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>

      <main className="main-content">
        {/* Grid Controls */}
        {images.length > 0 && (
          <div className="grid-controls">
            <div className="grid-layout-controls">
              <button
                className={`layout-btn ${gridLayout === 'auto' ? 'active' : ''}`}
                onClick={() => setGridLayout('auto')}
              >
                Auto
              </button>
              <button
                className={`layout-btn ${gridLayout === '2x2' ? 'active' : ''}`}
                onClick={() => setGridLayout('2x2')}
              >
                2×2
              </button>
              <button
                className={`layout-btn ${gridLayout === '3x3' ? 'active' : ''}`}
                onClick={() => setGridLayout('3x3')}
              >
                3×3
              </button>
              <button
                className={`layout-btn ${gridLayout === '4x4' ? 'active' : ''}`}
                onClick={() => setGridLayout('4x4')}
              >
                4×4
              </button>
            </div>

            <div className="filter-sort-controls">
              <input
                type="text"
                placeholder="Filter by prompt..."
                value={filterPrompt}
                onChange={(e) => setFilterPrompt(e.target.value)}
                className="filter-input"
              />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="prompt">By Prompt</option>
              </select>
            </div>

            <div className="selection-controls">
              <button
                className="select-all-btn"
                onClick={selectAllImages}
              >
                {selectedImages.size === images.length ? 'Deselect All' : 'Select All'}
              </button>
              {selectedImages.size > 0 && (
                <>
                  <span className="selection-count">
                    {selectedImages.size} selected
                  </span>
                  <div className="batch-actions">
                    <button
                      className="batch-btn download-btn"
                      onClick={downloadSelectedImages}
                      title="Download selected images"
                    >
                      ↓ Download
                    </button>
                    <button
                      className="batch-btn delete-btn"
                      onClick={deleteSelectedImages}
                      title="Delete selected images"
                    >
                      × Delete
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Image Grid */}
        <div className={getGridClass()}>
          {isGenerating && images.length === 0 && (
            Array(batchSize).fill(null).map((_, index) => (
              <div key={index} className="image-placeholder loading">
                <div className="loading-shimmer"></div>
              </div>
            ))
          )}
          {getPaginatedImages().map((image) => (
            <div
              key={image.id}
              className={`image-item ${selectedImages.has(image.id) ? 'selected' : ''}`}
            >
              <div className="image-checkbox">
                <input
                  type="checkbox"
                  checked={selectedImages.has(image.id)}
                  onChange={() => toggleImageSelection(image.id)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <img
                src={image.url}
                alt={image.prompt}
                onClick={() => setLightboxImage(image)}
              />
            </div>
          ))}
        </div>

        {/* Pagination Controls */}
        {images.length > imagesPerPage && (
          <div className="pagination-controls">
            <div className="pagination-info">
              <span>
                Showing {Math.min((currentPage - 1) * imagesPerPage + 1, getFilteredAndSortedImages().length)} - {Math.min(currentPage * imagesPerPage, getFilteredAndSortedImages().length)} of {getFilteredAndSortedImages().length} images
              </span>
            </div>

            <div className="pagination-buttons">
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                ««
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                ‹
              </button>

              <span className="page-indicator">
                Page {currentPage} of {getTotalPages()}
              </span>

              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(prev => Math.min(getTotalPages(), prev + 1))}
                disabled={currentPage === getTotalPages()}
              >
                ›
              </button>
              <button
                className="pagination-btn"
                onClick={() => setCurrentPage(getTotalPages())}
                disabled={currentPage === getTotalPages()}
              >
                »»
              </button>
            </div>

            <div className="per-page-controls">
              <label>Per page:</label>
              <select
                value={imagesPerPage}
                onChange={(e) => {
                  setImagesPerPage(Number(e.target.value))
                  setCurrentPage(1)
                }}
                className="per-page-select"
              >
                <option value={8}>8</option>
                <option value={16}>16</option>
                <option value={32}>32</option>
                <option value={64}>64</option>
              </select>
            </div>
          </div>
        )}
      </main>

      {/* Lightbox Modal */}
      {lightboxImage && (
        <div
          className="lightbox-overlay"
          onClick={() => setLightboxImage(null)}
        >
          <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
            <button
              className="lightbox-close"
              onClick={() => setLightboxImage(null)}
            >
              ×
            </button>
            <img
              src={lightboxImage.url}
              alt={lightboxImage.prompt}
              className="lightbox-image"
            />
            <div className="lightbox-info">
              <p>{lightboxImage.prompt}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default App

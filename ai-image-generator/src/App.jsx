import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [prompt, setPrompt] = useState('car')
  const [apiKey, setApiKey] = useState('')
  const [saveApiKey, setSaveApiKey] = useState(false)
  const [batchSize, setBatchSize] = useState(4)
  const [model, setModel] = useState('imagen-4.0-generate-preview-06-06')
  const [size, setSize] = useState('1024x1024')
  const [selectedStyle, setSelectedStyle] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [images, setImages] = useState([])
  const [showApiKey, setShowApiKey] = useState(false)

  const BASE_URL = 'https://api.voidai.app/v1'
  const API_KEY_STORAGE_KEY = 'voidai_api_key'

  const styles = [
    'Cinematic', '3D Render', 'Minimalist', 'Anime',
    'Photorealistic', 'Fantasy', 'Abstract', 'Vintage'
  ]

  // Load API key on mount
  useEffect(() => {
    const savedKey = localStorage.getItem(API_KEY_STORAGE_KEY)
    if (savedKey) {
      setApiKey(savedKey)
      setSaveApiKey(true)
    }
  }, [])

  // Save/remove API key
  useEffect(() => {
    if (saveApiKey && apiKey) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey)
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY)
    }
  }, [saveApiKey, apiKey])

  const generateImages = async () => {
    if (!prompt.trim() || !apiKey.trim()) {
      alert('Please enter a prompt and API key.')
      return
    }

    setIsGenerating(true)
    setImages([])

    const fullPrompt = selectedStyle ? `${prompt}, ${selectedStyle}` : prompt

    try {
      const promises = Array(batchSize).fill(null).map(async (_, index) => {
        const response = await fetch(`${BASE_URL}/images/generations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model,
            prompt: fullPrompt,
            n: 1,
            size,
            seed: Math.floor(Math.random() * 1000000)
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error?.message || `HTTP Error ${response.status}`)
        }

        const data = await response.json()
        return { id: index, url: data.data[0].url, prompt: fullPrompt }
      })

      const results = await Promise.all(promises)
      setImages(results)
    } catch (error) {
      console.error('Error generating images:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="app">
      <aside className="sidebar">
        <div className="sidebar-content">
          <div className="prompt-section">
            <input
              type="text"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your prompt..."
              className="prompt-input"
            />
          </div>

          <div className="style-section">
            <label className="section-label">Style</label>
            <div className="style-chips">
              {styles.map((style) => (
                <button
                  key={style}
                  className={`style-chip ${selectedStyle === style ? 'active' : ''}`}
                  onClick={() => setSelectedStyle(selectedStyle === style ? '' : style)}
                >
                  {style}
                </button>
              ))}
            </div>
          </div>

          <div className="settings-section">
            <div className="setting-group">
              <label className="section-label">Images: {batchSize}</label>
              <input
                type="range"
                min="1"
                max="16"
                value={batchSize}
                onChange={(e) => setBatchSize(parseInt(e.target.value))}
                className="slider"
              />
            </div>

            <div className="setting-group">
              <label className="section-label">Model</label>
              <select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                className="select"
              >
                <option value="imagen-4.0-generate-preview-06-06">Imagen 4.0</option>
                <option value="imagen-4.0-ultra-generate-preview-06-06">Imagen 4.0 Ultra</option>
              </select>
            </div>

            <div className="setting-group">
              <label className="section-label">Size</label>
              <select
                value={size}
                onChange={(e) => setSize(e.target.value)}
                className="select"
              >
                <option value="1024x1024">1:1 (1024x1024)</option>
                <option value="1408x768">16:9 (1408x768)</option>
                <option value="768x1408">9:16 (768x1408)</option>
              </select>
            </div>

            <div className="setting-group">
              <label className="section-label">API Key</label>
              <div className="api-key-input">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your API key"
                  className="input"
                />
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="toggle-btn"
                >
                  {showApiKey ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={saveApiKey}
                  onChange={(e) => setSaveApiKey(e.target.checked)}
                />
                Save API Key
              </label>
            </div>
          </div>

          <button
            onClick={generateImages}
            disabled={isGenerating}
            className="generate-btn"
          >
            {isGenerating ? 'Generating...' : 'Generate'}
          </button>
        </div>
      </aside>

      <main className="main-content">
        <div className="image-grid">
          {isGenerating && images.length === 0 && (
            Array(batchSize).fill(null).map((_, index) => (
              <div key={index} className="image-placeholder loading">
                <div className="loading-shimmer"></div>
              </div>
            ))
          )}
          {images.map((image) => (
            <div key={image.id} className="image-item">
              <img src={image.url} alt={image.prompt} />
            </div>
          ))}
        </div>
      </main>
    </div>
  )
}

export default App
